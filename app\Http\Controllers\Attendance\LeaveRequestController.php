<?php

namespace App\Http\Controllers\Attendance;

use App\Http\Controllers\Controller;
use App\Http\Requests\Attendance\Leave\LeaveRequestUpdateRequest;
use App\Http\Requests\Attendance\Leave\RequestedLeaveDeleteRequest;
use App\Models\Attendance\Leave\ExtraEntertainmentLeave;
use App\Models\User;
use App\Repositories\Attendance\Attendance\LeaveRequestRepository;
use App\Repositories\Attendance\Holiday\HolidayRepository;
use App\Repositories\Attendance\Leave\LeaveRepository;
use App\Repositories\Attendance\Notification\AttendanceNotificationRepository;
use App\Repositories\MasterData\LeaveTypeRepository;
use App\Repositories\MasterData\ProvinceRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\Tashkilat\Department\DepartmentRepository;
use App\Traits\ResponseTrait;
use Illuminate\Support\Facades\View;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;
use function PHPUnit\Framework\isEmpty;

class LeaveRequestController extends Controller
{
    use ResponseTrait;

    // repositories
    private $leaveRepository;
    private $leaveTypeRepository;
    private $employeeRepository;
    private $departmentRepository;
    private $leaveRequestRepository;
    private $holidayRepository;
    private $attendanceNotificationRepository;
    private $provinceRepository;



    public function __construct(LeaveRepository $leaveRepository, ProvinceRepository $provinceRepository, HolidayRepository $holidayRepository, LeaveTypeRepository $leaveTypeRepository, EmployeeRepository $employeeRepository, DepartmentRepository $departmentRepository, LeaveRequestRepository $leaveRequestRepository, AttendanceNotificationRepository $attendanceNotificationRepository)
    {
        $this->middleware('auth');
        $this->leaveRepository = $leaveRepository;
        $this->leaveRepository = $leaveRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->employeeRepository = $employeeRepository;
        $this->departmentRepository = $departmentRepository;
        $this->holidayRepository = $holidayRepository;
        $this->leaveRequestRepository = $leaveRequestRepository;
        $this->attendanceNotificationRepository = $attendanceNotificationRepository;
        $this->provinceRepository = $provinceRepository;
    }
    private function validateTotalLeave($leave_type, $amount, $empID)
    {

        $total = $this->leaveRepository->getTotalTakenLeaveByType($leave_type, $empID);

        switch ($leave_type) {

            case '1':
                if ($total + $amount > 10) {
                    return __('general.the_number_of_days_of_this_leavetype_has_completed');
                }
                return true;
                break;
            case '2':
                if ($total + $amount > 20) {
                    return __('general.the_number_of_days_of_this_leavetype_has_completed');
                }
                return true;
                break;
            case '3':
                if ($total + $amount > 20) {
                    return __('general.the_number_of_days_of_this_leavetype_has_completed');
                }
                return true;
                break;
            case '11':
                $extra_entertainment_leaves = ExtraEntertainmentLeave::where('employee_id', $empID)->where('to_year', getCurrentShamsiYear())->get();
                if (!is_null($extra_entertainment_leaves) && count($extra_entertainment_leaves) > 0) {
                    if ($amount > 20) {
                        return __('attendance/att.extra_entertainment_leave_is_only_20_days');
                    } else {
                        $extra = $extra_entertainment_leaves->first();
                        if (($total + $amount) > $extra->days_no) {
                            $remainder = $extra->days_no - $total;
                            return __('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]);
                        }
                    }
                } else {
                    return __('attendance/att.employee_is_not_eligible_for_extra_entertainment_leave');
                }

                return true;
                break;

            default:
                return null;
                break;
        }
    }
    private function leaveCreatedNotification($employee, $leaveRequest)
    {

        $dataNotification = [
            'department_id' => $employee->department_id,
            'source_id' => $leaveRequest->id,
            'employee_id' => $employee->id,
            'user_type' => $leaveRequest['user_type'],
            'priority' => in_array($employee->emp_bast_id, [12, 11, 17]) == true ? 2 : 1,
        ];
        return $this->attendanceNotificationRepository->store($dataNotification);
    }

    //
    public function index(Request $request)
    {
        $empID = auth()->user()->employee->id;
        $perPage = $request->input('perPage', 10);

        $datas = $this->leaveRequestRepository->getEmployeeLeaveRequests($empID, getCurrentShamsiYear(), $perPage);

        if ($request->ajax()) {
            return view('components.pages.attendance.attendance.leave_request.leave_request_datatable', ['datas' => $datas]);
        }

        return view('pages.attendance.leave_request.index', [
            'datas' => $datas,
            'pager' => withPaginate(['searchRouteName' => 'attendance.holidays.search'])
        ]);
    }
    public function executiveIndex(Request $request, $empID)
    {

        $employeeId = decrypter($empID);

        $perPage = $request->input('perPage', 10);

        $datas = $this->leaveRequestRepository->getEmployeeLeaveRequests($employeeId, getCurrentShamsiYear(), $perPage);
        $dataEmployee = $this->employeeRepository->getEmployeeById($employeeId);
        if (!in_array($dataEmployee->employee_mawqif_id, [1, 2])) {
            return redirect()->back()->with(['error' => __('general.only_barhal_employees_can_request')]);
        }
        $full_name = $dataEmployee->name . ' ' . $dataEmployee->last_name;
        if ($request->ajax()) {
            return view('components.pages.attendance.attendance.leave_request.leave_request_datatable', ['datas' => $datas, 'selected_emp' => $empID]);
        }

        return view('pages.attendance.leave_request.executive-index', [
            'datas' => $datas,
            'selected_emp' => $empID,
            'full_name' => $full_name,
            'pager' => withPaginate(['searchRouteName' => 'attendance.holidays.search'])
        ]);
    }
    public function getLeaveRequestLeaveTypes(Request $request)
    {

        try {
            $selected_emp = isset($request->selected_emp) == true ? $request->selected_emp : null; // if request comes as indevidual or by executive manager
            $dataLeaveTypes = $this->leaveTypeRepository->getLeaveRequestLeaveTypes();
            $provinces = $this->provinceRepository->getAllProvinces();
            return view('components.pages.attendance.attendance.leave_request.create_leave_request', ['leave_types' => $dataLeaveTypes, 'provinces' => $provinces, 'selected_emp' => $selected_emp]);
        } catch (\Exception $th) {
            return  $this->getErrorResponse($request->all(), __('general.something_wrong_happened'));
        }
    }
    public function leaveRequestLeaveTypesChanges(Request $request)
    {

        $leave_for_employee_id = decrypter($request->selected_user, false, false, true) ?? auth()->user()->employee_id; // here if request has selected_user it means someone is requesting leave request for another user if its null the user is requesting for him self 
        if (!$request->leave_start_date || !$request->leave_end_date) {
            return redirect()->back()->with('error', __('general.something_wrong_happened'));
        };
        $miladiStartDate = dateTo($request->leave_start_date, 'miladi', false);
        $miladiEndDate = dateTo($request->leave_end_date, 'miladi', false);
        $amount = getNumberOfDaysByDates($miladiStartDate, $miladiEndDate);
        return $this->validateTotalLeave(decrypter($request->type), $amount, $leave_for_employee_id);
    }


    public function postLeaveRequst(Request $request)
    {

        try {

            FacadesDB::beginTransaction();
            $requested_leave_days = (int)floor((strtotime($request->leave_end_date) - strtotime($request->leave_start_date)) / (60 * 60 * 24)) + 1;
            $employee = [];

            if (isset($request->selected_emp)) { // if others set leave for an employee 
                $employee =  $this->employeeRepository->getEmployeeById(decrypter($request->selected_emp));
                if (!$employee->user) {
                    $request['user_type'] = 1;
                } else {
                    $request['user_type'] = $employee->user->type;
                }
            } else {
                // if employee set leves for himself/herself;

                $employee =  auth()->user()->employee;

                $request['user_type'] = auth()->user()->type;
            }

            $leaveValidDate = $this->leaveRepository->getLeaveValidDate(dateTo($request->leave_start_date, 'miladi', false));
            if ($leaveValidDate['status'] == false) {
                return redirect()->back()->with('error', $leaveValidDate['statusMessage']);
            }
            // check if the leave is requested within 3 days of leave end date, and suspended for some legal action of attendance department
            // $validDays = (int)floor((strtotime(getCurrentShamsiDate()) - strtotime($request->leave_end_date)) / (60 * 60 * 24));
            // if ($validDays > 3){
            //     return redirect()->back()->with('error', __('attendance/att.too_late_to_apply_for_leave'));
            // }

            // leave type
            $request['leave_type'] =  decrypter($request->leave_type);

            if ($requested_leave_days > 10 && $request['leave_type'] == 1) { // Only allow 10 days necessary leave
                return redirect()->back()->with('error', __('attendance/att.necessary_leave_is_only_10_days'));
            } elseif ($requested_leave_days > 20 && $request['leave_type'] == 2) { // Only allow 20 days entertainment leave
                return redirect()->back()->with('error', __('attendance/att.entertainment_leave_is_only_20_days'));
            } elseif ($requested_leave_days > 5 && $request['leave_type'] == 3) { // only allow 5 day of sick leave
                return redirect()->back()->with('error', __('attendance/att.request_sick_leave_is_only_5_days'));
            }

            $days_of_week = date('l', strtotime(dateTo($request->leave_start_date, 'miladi', false)));
            if ($days_of_week == 'Friday') {
                return redirect()->back()->with('error', __('attendance/att.leave_request_at_firday', ['date' => $request->leave_start_date]));
            } else {
                $isholidy = $this->holidayRepository->getHolidaysByDate(dateTo($request->leave_start_date, 'miladi', false));
                if (!is_null($isholidy) && count($isholidy) > 0) {
                    return redirect()->back()->with('error', __('attendance/att.leave_request_at_holiday', ['date' => $request->leave_start_date]));
                }
            }



            $miladiStartDate = $request['leave_start_date'] = dateTo($request->leave_start_date, 'miladi', false);
            $miladiEndDate = $request['leave_end_date'] = dateTo($request->leave_end_date, 'miladi', false);
            $amount = getNumberOfDaysByDates($miladiStartDate, $miladiEndDate);

            $isValide = $this->validateTotalLeave($request['leave_type'], $amount, $employee->id);
            if ($isValide != true) {
                return redirect()->back()->with('error', $isValide);
            }
            $isValide = $this->leaveRepository->isValidLeave($employee->id, $miladiStartDate);
            if ($isValide != true) {
                return redirect()->back()->with('error', $isValide);
            }
            $leaveRequestCreated = $this->leaveRequestRepository->postLeaveRequst($request, $employee->id);

            if ($leaveRequestCreated) {
                // here we need to fire leave created notification 
                $this->leaveCreatedNotification($employee, $leaveRequestCreated);
                FacadesDB::commit();

                return redirect()->back()->with('success', __('general.successful'));
            }
            return redirect()->back()->with('error', __('general.something_wrong_happened'));
        } catch (\Exception $ex) {
            FacadesDB::rollback();

            return redirect()->back()->with('error', $ex->getMessage());
        }
    }



    /**
     * Get one record of employee leave request [رخصتی]
     * @param  request contains leave-request id param
     */
    public function showLeaveRequestEditForm($id, Request $request)
    {
        try {
            $id = decrypter($id);
            // get the record from repository
            $data['leave'] = $this->leaveRequestRepository->getLeaveRequestById($id);
            if ($data['leave']->status == 1) {
                $data['leave_types'] = $this->leaveTypeRepository->getLeaveRequestLeaveTypes();
                $data['leave_start_date'] = dateTo($data['leave']->date_from, 'shamsi', false);
                $data['leave_end_date'] = dateTo($data['leave']->date_to, 'shamsi', false);

                // render the component
                $rendered_leave = View::make('components.pages.attendance.attendance.leave_request.edit_modal', ['data' => $data])->render();

                // send success response
                return $this->getSuccessResponse(['rendered_leave' => $rendered_leave, 'leave' => $data['leave']], trans('attendance/att.leave_fetched'));
            } else {
                return $this->getErrorResponse('', __('general.operation_failed'));
            }
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            dd($ex);
            //throw $ex;
        }
    }
    /**
     * Update leave request record
     * The request is validated in use LeaveUpdateRequest
     * @param  request contains leave_id and all the data param
     */
    public function update(LeaveRequestUpdateRequest $request)
    {
        try {

            $leave_id = decrypter($request->leave_id);
            $leaveRequest = $this->leaveRequestRepository->getLeaveRequestByID($leave_id);
            $employee = $this->employeeRepository->getEmployeeById($leaveRequest->employee_id);
            $start_date = $request->leave_start_date_edit;
            $end_date = $request->leave_end_date_edit;
            $requested_leave_days = (int)floor((strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24)) + 1;

            $leaveValidDate = $this->leaveRepository->getLeaveValidDate($start_date);
            if ($leaveValidDate['status'] == false) {
                return $this->getErrorResponse(null, $leaveValidDate['statusMessage']);
            }

            $leave_type = $request->leave_type_edit;

            if ($requested_leave_days > 10 && $leave_type == 1) { // Only allow 10 days necessary leave
                return $this->getErrorResponse(null,  __('attendance/att.necessary_leave_is_only_10_days'));
            } elseif ($requested_leave_days > 20 && $leave_type == 2) { // Only allow 20 days entertainment leave
                return $this->getErrorResponse(null,  __('attendance/att.entertainment_leave_is_only_20_days'));
            } elseif ($requested_leave_days > 5 && $leave_type == 3) { // only allow 5 day of sick leave
                return $this->getErrorResponse(null,  __('attendance/att.request_sick_leave_is_only_5_days'));
            }

            //leave request
            $days_of_week = date('l', strtotime($start_date));

            if ($days_of_week == 'Friday') {
                return $this->getErrorResponse(null,  __('attendance/att.leave_request_at_firday', ['date' => $start_date]));
            } else {
                $isholiday = $this->holidayRepository->getHolidaysByDate($start_date);
                if (!is_null($isholiday) && count($isholiday) > 0) {
                    return $this->getErrorResponse(null, __('attendance/att.leave_request_at_holiday', ['date' => $start_date]));
                }
            }
            $amount = getNumberOfDaysByDates($start_date, $end_date);

            $isvalid = $this->validateTotalLeave($leave_type, $amount, $employee->id);
            if ($isvalid !== true) {
                return $this->getErrorResponse(null, $isvalid);
            }
            $updated_leave = $this->leaveRequestRepository->update($request, $leaveRequest);
            if ($updated_leave != false) {
                return $this->getSuccessResponse(null, __('attendance/att.leave_record_updated'));
            }
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_update_failed'), $ex);
        }
    }

    public function getLeaveRequests(Request $request)
    {
        try {
            $empID = auth()->user()->employee->id;
            $perPage = $request->input('perPage', 10);
            $datas = $this->leaveRequestRepository->getEmployeeLeaveRequests($empID, getCurrentShamsiYear(), $perPage);
            // render the datatable
            $rendered_leaves = View::make('components.pages.attendance.attendance.leave_request.leave_request_datatable', ['datas' => $datas])->render();
            // send success response
            return $this->getSuccessResponse(['leave_request_datatable' => $rendered_leaves], trans('attendance/att.employee_leaves_fetched'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.employee_leaves_fetch_failed'), null, $ex);
        }
    }

    public function delete(RequestedLeaveDeleteRequest $request)
    {
        try {
            $leaveRequest = $this->leaveRequestRepository->getLeaveRequestByID($request->leave_id);
            if ($leaveRequest) {
                // delete leave
                $deleted = $this->leaveRequestRepository->delete($request->leave_id);
                $this->attendanceNotificationRepository->delete($request->leave_id);

                // deleted successfully leave record
                if ($deleted) {
                    return $this->getSuccessResponse(null, trans('attendance/att.leave_request_record_deleted'));
                }
            }
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_request_record_delete_failed'), $ex);

            //throw $ex;
        }
    }

    public function manageLeaveRequst(Request $request)
    {
        try {
            $perPage = $request->input('perPage', 10);
            $userDep[] = auth()->user()->employee->department_id;
            // incase if user has other deps access 
            $userDepChilds = Arr::flatten($this->departmentRepository->getUserDepartmentByFilter('child', null, 1, true)->pluck('id'));
            $userDep = array_merge($userDep, $userDepChilds);
            // Reset the keys (optional)
            $userDep = array_values(array_unique($userDep));

            $dataLeaveRequests = $this->employeeRepository->getEmpForLeaveManagement($userDep, $perPage);

            // parent departments
            if (auth()->user()->type == 6) { // super admin has full right
                $parent_departments = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false, false);
            } else { // other types has access to what departments are attached to them
                $parent_departments = $this->departmentRepository->getUserDepartmentByFilter('parent', null, 1, null);
            }


            if ($request->ajax()) {
                return view('components.pages.attendance.attendance.leave_request.leave_request_manage_datatable', ['datas' => $dataLeaveRequests]);
            }
            return view('pages.attendance.leave_request.manage_leave_request', [
                'datas' => $dataLeaveRequests,
                'pager' => withPaginate(['searchRouteName' => 'attendance.leave-request.manage.search']),
                'parent_departments' => $parent_departments
            ]);
        } catch (\Exception $ex) {
            dd($ex);
            return redirect()->back()->with('error', $ex->getMessage());
        }
    }

    public function searchLeaveRequests(Request $request)
    {
        try {
            // get data from repository
            $userDeps = $this->departmentRepository->getUserDeps(auth()->user()->id, true, ['department_id']);
            $dataLeaveRequests = $this->employeeRepository->searchEmpForLeaveManagement($request, $userDeps);

            // return view with data
            return view('components.pages.attendance.attendance.leave_request.leave_request_manage_datatable', ['datas' => $dataLeaveRequests]);
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('recruitment/recruitment.search_employees_failed'), $ex);
        }
    }

    public function getLeaveRequestApproveTemplate(Request $request)
    {
        try {

            if (!$request->requestLeaveRefID) {
                return response(__('general.something_wrong_happened'), 402);
            }
            // we need to set notification to readed 
            $this->attendanceNotificationRepository->setNotificationAsReaded(decrypter($request->requestLeaveRefID));
            return view('components.pages.attendance.attendance.leave_request.leave_request_approve_template', [
                'requestLeaveRefID' => $request->requestLeaveRefID
            ]);
        } catch (\Exception $ex) {
            return redirect()->back()->with('error', $ex->getMessage());
        }
    }
    public function getLeaveRequestShowDetailsTemplate(Request $request)
    {
        try {
            if (!$request->requestLeaveRefID) {
                return response(__('general.something_wrong_happened'), 402);
            }
            $provinces = $this->provinceRepository->getAllProvinces();
            return view('components.pages.attendance.attendance.leave_request.leave_request_view_details_template', [
                'dataDetails' =>  $this->leaveRequestRepository->getLeaveRequestByID(decrypter($request->requestLeaveRefID)),
                'provinces' => $provinces
            ]);
        } catch (\Exception $ex) {
            return redirect()->back()->with('error', $ex->getMessage());
        }
    }
    public function postLeaveRequestResponse(Request $request)
    {

        try {

            FacadesDB::beginTransaction();

            $leaveRequestID = decrypter($request->requestLeaveRefID);
            $leaveRequest = $this->leaveRequestRepository->getLeaveRequestByID($leaveRequestID);
            $status = decrypter($request->response_status);
            $userType = auth()->user()->type;
            if ($leaveRequest && $leaveRequest->status == 1 &&  in_array($userType, [User::$type['department-user'], User::$type['chairman']])) { // department level approve of a leave request

                if ($status == 2) { // set for pprove
                    $leaveRequest['status'] = 2;
                    $leaveRequest['accepted_at'] = now();
                    $leaveRequest['accepted_by'] = auth()->user()->id;
                    $leaveRequest['response_remark'] = $request->response_remark;
                    $leaveRequest->save();
                } elseif ($status == 3) { // set for reject
                    $leaveRequest['status'] = 3;
                    $leaveRequest['rejected_at'] = now();
                    $leaveRequest['rejected_by'] = auth()->user()->id;
                    $leaveRequest['response_remark'] = $request->response_remark;
                    $leaveRequest->save();
                }
                FacadesDB::commit();
                //     return $this->getSuccessResponse(null, trans('attendance/att.announcement_record_created'));
                //    return $this->getErrorResponse(null, trans('recruitment/recruitment.search_employees_failed'), $ex);
                return $this->getSuccessResponse(null, __('general.successful'));
            } elseif ($leaveRequest && $leaveRequest->status == 2 && in_array($userType, [User::$type['hr-staff']]) && canDo('attendance-normal_leave_registration')) {
                // hr level approve of a leave request 
                $request['leave_start_date'] = $leaveRequest->date_from;
                $request['leave_end_date'] = $leaveRequest->date_to;
                $request['leave_type'] = $leaveRequest->leave_type_id;
                $request['employee_id'] = $leaveRequest->employee_id;
                $request['leave_request_id'] = $leaveRequestID;

                $amount = getNumberOfDaysByDates($request['leave_start_date'], $request['date_to']);

                $isValide = $this->validateTotalLeave($request['leave_type'], $amount, $request['employee_id']);
                if ($isValide != true) {
                    return $this->getErrorResponse(null, $isValide);
                }

                $leaves = $this->leaveRepository->getLeavesByDate($request['leave_start_date'], $request['employee_id']);


                if (!is_null($leaves) && count($leaves) > 0 && $status == 2) {
                    // check if all existing leave has leave_type_id 9 or 10 which is otherissues and booklet
                    $allLeavesAreSpecialType = true;
                    foreach ($leaves as $leave) {
                        if (!in_array($leave->leave_type_id, [9, 10])) {
                            $allLeavesAreSpecialType = false;
                            break;
                        }
                    }
                    // only show error if no special leave types (9 or 10) are found
                    if (!$allLeavesAreSpecialType) {
                        return $this->getErrorResponse(null, __('attendance/att.mutiple_leaves_at_same_date', ['date' => $request['leave_start_date'] . ' | ' . $request['leave_end_date']]));
                    }
                }

                if ($status == 2) { // hr approve leavel
                    $isLeaveSavedSuccessfully = $this->leaveRepository->store($request);

                    if ($isLeaveSavedSuccessfully && $request['leave_type'] == 11) {
                        $extra = ExtraEntertainmentLeave::where('employee_id', $request['employee_id'])->where('to_year', getCurrentShamsiYear())->first();
                        if ($extra) {
                            $isLeaveSavedSuccessfully->extra_entertainment_leave_id = $extra->id;
                            $isLeaveSavedSuccessfully->save();
                        }
                    }
                    if ($isLeaveSavedSuccessfully) {
                        $leaveRequest['status'] = 4; // hr approve indexer at leave request table
                        $leaveRequest['hr_accepted_at'] = now();
                        $leaveRequest['hr_accepted_by'] = auth()->user()->id;
                        $leaveRequest['hr_response_remark'] = $request->response_remark;
                        $leaveRequest->save();
                    }
                } elseif ($status == 3) {
                    $leaveRequest['status'] = 3;
                    $leaveRequest['rejected_at'] = now();
                    $leaveRequest['rejected_by'] = auth()->user()->id;
                    $leaveRequest['hr_response_remark'] = $request->response_remark;
                    $leaveRequest->save();
                }
                FacadesDB::commit();

                return $this->getSuccessResponse(null, __('general.successful'));
            }

            return $this->getErrorResponse(null, __('general.something_wrong_happened') . ' (' . __('general.not_completed') . ')');
           
        } catch (\Exception $ex) {
            FacadesDB::rollback();
            dd($ex);
            return redirect()->back()->with('error', __('general.something_wrong_happened'));
        }
    }
}
