<?php

namespace App\Http\Requests\Tashkilat\Tashkilat;

use Illuminate\Foundation\Http\FormRequest;

class JobDescriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
          //  'id' => 'required|exists:tashkilat,id',
        ];
    }

    /**
     * Prepare the data for validation.
     * This method is called before rules function
     */
    protected function prepareForValidation(): void
    {
        // Just decrypting the id which is encrypted
        $this->merge([
         //   'id' => $this->id != 0 ? decrypt($this->id) : $this->id,
        ]);
    }
}
