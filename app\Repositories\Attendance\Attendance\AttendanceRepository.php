<?php

namespace App\Repositories\Attendance\Attendance;

use App\Interfaces\Attendance\Attendance\AttendanceInterface;
use App\Models\Attendance\Image;
use App\Traits\ApplyDepartmentFilter;
use App\Traits\DepartmentFilterTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Month;

class AttendanceRepository implements AttendanceInterface
{
    use ApplyDepartmentFilter;
    /**
     * Get employees [کارمندان]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function getEmployees($perPage)
    {
        try {
            // build the query
            $query = $this->buildEmployeesQuery('data', null, [1, 2, 3]);

            // paginate data
            $data = $query->paginate($perPage);

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Search employees [جستجوی کارمندان]
     * This function returns only the requests amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function searchEmployees(Request $request)
    {
        try {
            // per page
            $perPage = $request->input('perPage', 10);

            // employee types
            $employee_types = $request->employee_type;

            // build the query
            $query = $this->buildEmployeesQuery('data', $request, $employee_types);

            // paginate data
            $data = $query->paginate($perPage);

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Get continues absent employees [کارمندان که دارای رخصتی متواتر است]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param perPage
     */
    public function getContinuesAbsentEmployees($perPage)
    {
        try {
            // build the query
            $query = $this->buildContinuesAbsentEmployeesQuery('data', 0, null, [1, 2, 3]);

            // paginate data
            $data = $query->paginate($perPage);

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Search continues absent employees [جستجوی کارمندان که دارای رخصتی متواتر است]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function searchContinuesAbsentEmployees(Request $request)
    {
        try {
            // per page
            $perPage = $request->input('perPage', 10);

            // employee types
            $employee_types = $request->employee_type;

            // read type
            $read_status = $request->read_status;

            // build the query
            $query = $this->buildContinuesAbsentEmployeesQuery('data', $read_status, $request, $employee_types);

            // paginate data
            $data = $query->paginate($perPage);

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * get employee images
     */
    public function getEmployeeImages($rfid, $year, $month)
    {
        try {

            $sday = att_month_days(0);
            $eday = getLastDayOfShamsiMonth($year, $month);

            //  dd($sday,$eday,$year,$month);
            $year = (int)$year;
            $month = (int)$month;
            $sday = (int)$sday;
            $eday = (int)$eday;
            // if ($month == 1) {
            //     $from = dateToMiladi($year - 1, 12, $sday);
            // } else {
            //     $from = dateToMiladi($year, $month - 1, $sday); //the start day of attendance month is 11 of previous month
            // }


            $from = dateToMiladi($year, $month, $sday);
            $to = dateToMiladi($year, $month, $eday); //the end day of attendance month is 11 of current month

            $data = FacadesDB::table('attendance_images')
                ->select('id', 'path', 'status', 'date')
                ->where('RFID', $rfid) //check the rfid
                ->where('date', '>=', $from)
                ->where('date', '<=', $to)
                ->orderBy("date")
                ->orderBy("time")
                ->get();

            return $data;
        } catch (\Exception $ex) {
            dd($ex);
        }
    }

    /**
     * get employee images by date
     */
    public function getEmployeeImagesByDate($rfid, $date)
    {
        try {
            $data = FacadesDB::table('attendance_images')
                ->select('id', 'path', 'status', 'date')
                ->where('RFID', $rfid) //check the rfid
                ->where('date', $date)
                ->orderBy("date")
                ->orderBy("time")
                ->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * get employee images between start and end date
     */
    public function getEmployeeImagesBetweenDates($rfid, $start_date, $end_date)
    {
        try {
            $data = FacadesDB::table('attendance_images')
                ->select('id', 'path', 'date')
                ->where('RFID', $rfid) //check the rfid
                ->where('date', '>=', $start_date)
                ->where('date', '<=', $end_date)
                ->orderBy("date")
                ->orderBy("time")
                ->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * get employees images by employees RFIDs and date
     */
    public function getEmployeeImagesByRFIDs($rfids, $date)
    {
        try {
            $data = FacadesDB::table('attendance_images')
                ->select('id', 'RFID', 'path', 'date', 'time')
                ->whereIn('RFID', $rfids) //check the rfids
                ->where('date', $date)
                ->orderBy("date")
                ->orderBy("time")
                ->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * get employees images by employees RFIDs and between start_date and end_date
     */
    public function getEmployeeImagesBetweenDatesByRFIDs($rfids, $start_date, $end_date)
    {
        try {
            $data = FacadesDB::table('attendance_images')
                ->select('id', 'RFID', 'path', 'status', 'date', 'time')
                ->whereIn('RFID', $rfids) //check the rfids
                ->where('date', '>=', $start_date)
                ->where('date', '<=', $end_date)
                ->orderBy("date")
                ->orderBy("time")
                ->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Get image by id
     * @param id image id
     */
    public function getImageById($id)
    {
        try {
            if (!is_null($id)) {
                return Image::find($id);
            }
            return null;
        } catch (\Exception $ex) {
            return null;
        }
    }

    /**
     * Reject image
     * The request is validated in use ImageRejectionRequest
     * @param image_id
     */
    public function rejectImage($image_id)
    {
        try {
            if (!empty($image_id)) {
                $image = $this->getImageById($image_id);
                if (!is_null($image)) {
                    $image->delete();
                    return true;
                }
            }
            return false;
        } catch (\Exception $ex) {
            return false;
        }
    }

    public function getEmployeeAttendanceDates($from, $to, $rfid)
    {
        try {
            $tataEmployeeDays = FacadesDB::table('attendance_images AS atImg')->select(
                'date'
            )
                ->where('atImg.RFID', $rfid)
                ->where('atImg.status', '0')
                ->whereRaw("atImg.date >= '" . $from . "' AND " . " atImg.date <= '" . $to . "'")
                ->orderBy('atImg.date', 'ASC')->get();
            $AllPresentDays = [];

            //  we need to only take only those dates which emplyee has 2 images not only 1 
            for ($i = 0; $i < count($tataEmployeeDays) - 1; $i++) {
                if ($tataEmployeeDays[$i]->date == $tataEmployeeDays[$i + 1]->date) {
                    array_push($AllPresentDays, $tataEmployeeDays[$i]->date);
                }
            }
            return $AllPresentDays;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    public function EmplyeeAbsentOrNotAbsent($countOfPresentdays, $CountOfMonthDays)
    {
        // 62 - 45 = 17 


        $AbsentDays = ($CountOfMonthDays - $countOfPresentdays);

        if ($AbsentDays > 16) {
            return [[true], [$AbsentDays]];
        } else {
            return [[false], [$AbsentDays]];
        }

        //return $AllPresentDays;
    }

    function InsertAtt_notify_fire_Data($datas = [])
    {

        if (count($datas['data']) > 0) {
            try {
                //code...
            } catch (\Throwable $th) {
                //throw $th;
            }
            $x = 0;
            $y = [];
            foreach ($datas['data'] as $data) {

                $userExist = $this->fireUserExist($data['emp_id']);
                array_push($y, [$userExist, $data['emp_id']]);
                if (count($userExist) > 0) {
                    $x++;
                    FacadesDB::table('attendance_notify_fire')->where('employee_id', $data['emp_id'])->update(
                        [
                            'days' => implode(",", $data['record']),
                            'date_from' => $datas['date_from'],
                            'date_to' => $datas['date_to'],
                            //'mark_read'=>$userExist['mark_read'],
                            'employee_id' => $data['emp_id'],
                            'created_by' => auth()->user()->id,
                        ]
                    );
                } else {

                    FacadesDB::table('attendance_notify_fire')->insert(
                        [
                            'days' => implode(",", $data['record']),
                            'date_from' => $datas['date_from'],
                            'date_to' => $datas['date_to'],
                            'employee_id' => $data['emp_id'],
                            'created_by' => auth()->user()->id,
                        ]
                    );
                }
            }

            return [$y];
        }
    }

    /**
     * Search employee for annual leaves report
     */
    public function searchEmployeesForAnnualLeavesReport($request, $type)
    {
        try {
            // per page
            $perPage = $request->input('perPage', 10);

            // build the query
            $query = $this->buildAnnualLeavesQuery('data', $request);

            // paginate data
            $data = $type == 'paginate' ? $query->paginate($perPage) : $query->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Search employee for annual leaves report
     */
    public function searchEmployeesReport($request, $type)
    {
        try {
            // per page
            $perPage = $request->input('perPage', 10);

            // build the query
            $query = $this->buildAttendanceQuery('data', $request);

            // paginate data
            $data = $type == 'paginate' ? $query->paginate($perPage) : $query->get();
            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Search e-attendance employees report
     */
    public function searchEAttendanceEmployeesReport($request, $type)
    {
        try {
            // per page
            $perPage = $request->input('perPage', 10);

            // build the query
            $query = $this->buildEAttendanceEmployeesQuery('data', $request);

            // paginate data
            $data = $type == 'paginate' ? $query->paginate($perPage) : $query->get();

            return $data;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    // HELPERS ---------------------------------------------------------------------------------------
    /**
     * Build employees query
     * @param employee_type has three values separatly or in array: 1: Employee, 2: Ajir, 3: Military
     * @param fetch_mode has three values: 'data', 'excel_report', 'count'
     */
    private function buildEmployeesQuery($fetch_mode, $request = null, $employee_type = null)
    {
        try {
            $query = FacadesDB::table('employees AS employees');

            if (!is_null($request)) {
                $id = $request->id;
                $name = $request->name;
                $parent_department = $request->parent_department != 0 ? decrypt($request->parent_department) : 0;
                $department = $request->department != 0 ? decrypt($request->department) : 0;
                $e_attendance_status = $request->e_attendance_status;
                $current_status = $request->current_status;
                $gender = $request->gender;

                // query for id of employee
                if (!is_null($id) && $id != '') {
                    $query = $query->where('employees.id', $id);
                }

                // query for name, last name and father name
                if (!is_null($name) && $name != '') {
                    $query = $query->where(function ($q) use ($name) {
                        $q->where('employees.name', 'like', '%' . $name . '%')
                            ->orWhere('employees.name_en', 'like', '%' . $name . '%')
                            ->orWhere('employees.last_name', 'like', '%' . $name . '%')
                            ->orWhere('employees.last_name_en', 'like', '%' . $name . '%')
                            ->orWhere('employees.father_name', 'like', '%' . $name . '%')
                            ->orWhere('employees.father_name_en', 'like', '%' . $name . '%');
                    });
                }

                // parent_department
                if (!is_null($parent_department) && $parent_department != '' && $parent_department != 0) {

                    $query = $query->where('employees.parent_department_id', $parent_department);
                }

                // department
                if (!is_null($department) && $department != '' && $department != 0) {

                    $query = $query->where('employees.department_id', $department);
                }

                // e attendance status [included, not included]
                if (!is_null($e_attendance_status) && count($e_attendance_status) > 0) {
                    // if count is not 1, then it mean include both status
                    if (count($e_attendance_status) == 1) {
                        // included in e attendance
                        if ($e_attendance_status[0] == 1) {
                            $query->whereNotNull('employees.rfid');
                        } else {
                            $query->whereNull('employees.rfid');
                        }
                    }
                }

                // employee current status
                if (!is_null($current_status) && $current_status != '' && $current_status != 0) {
                    $query = $query->where('employees.current_status', $current_status);
                }

                // gender
                if (!is_null($gender) && count($gender) > 0) {
                    $query = $query->whereIn('employees.gender', $gender);
                }
            }
            // Employee Type
            if (!is_null($employee_type) && is_array($employee_type) && count($employee_type) > 0) {
                $hasContractual = in_array(4, $employee_type);
                $onlyContractual = $hasContractual && count($employee_type) === 1; // Only [4] is selected

                $query->where(function ($q) use ($employee_type, $hasContractual, $onlyContractual) {
                    if ($onlyContractual) {
                        // If only the 4th checkbox (Contractual) is selected, show only is_contract = 1 employees
                        $q->where('employees.is_contract', 1);
                    } else {
                        // Regular filtering for employee types (excluding Contractual 4)
                        $q->whereIn('employees.employee_type_id', array_diff($employee_type, [4]));

                        // If "Contractual" is selected with other types, include is_contract = 1 employees
                        if ($hasContractual) {
                            $q->orWhere(function ($subQ) use ($employee_type) {
                                $subQ->where('employees.is_contract', 1)
                                    ->whereIn('employees.employee_type_id', $employee_type);
                            });
                        }
                    }
                });

                // If "Contractual" is NOT selected, exclude is_contract = 1
                if (!$hasContractual) {
                    $query->where(function ($q) {
                        $q->whereNull('employees.is_contract')->orWhere('employees.is_contract', '!=', 1);
                    });
                }
            } elseif (!is_null($employee_type)) {
                // Handle case when employee_type is a single value
                $query->where('employees.employee_type_id', $employee_type)
                    ->where(function ($q) {
                        $q->whereNull('employees.is_contract')->orWhere('employees.is_contract', '!=', 1);
                    });
            }

            if ($fetch_mode == 'data') {
                // select columns for datatable

                $query->select(
                    'employees.*',
                    'departments.name_' . app()->getLocale() . ' AS department',
                    'employee_types.name_' . app()->getLocale() . ' AS employee_type',
                    // 'employeed_types.name_' . app()->getLocale() . ' AS employeed_type',
                    'employee_statuses.name_' . app()->getLocale() . ' AS current_status_name',
                );
                // joins
                $query->leftjoin('departments', 'departments.id', '=', 'employees.department_id');
                $query->leftjoin('employee_types', 'employee_types.id', '=', 'employees.employee_type_id');
                // $query->leftJoin('employeed_types', 'employeed_types.id', '=', 'employees.employeed_type_id');
                $query->leftjoin('employee_statuses', 'employee_statuses.id', '=', 'employees.current_status');
            } else if ($fetch_mode == 'excel_report') {
                // select columns for excel report
                $query->select(
                    'employees.id AS employee_id',
                    'employees.name AS employee_name',
                    'employees.last_name AS employee_last_name',
                    'employees.name_en AS employee_name_en',
                    'employees.father_name AS father_name',
                    'employees.grand_father_name AS grand_father_name',
                    'employees.email AS email',
                    'employees.phone AS phone',
                    'employees.gender AS gender',
                    'employees.hire_date AS hire_date',
                    'employees.tanqis_date AS tanqis_date',
                    'employees.tanqis_no AS tanqis_number',
                    'employees.tanqis_type AS tanqis_type',
                    'employees.tanqis_remarks AS tanqis_remarks',
                    'employees.current_position_' . app()->getLocale() . ' AS current_position',
                    'employee_types.name_' . app()->getLocale() . ' AS employee_type_name',
                    DB::raw('(CASE WHEN employees.employee_type_id = 1 THEN employee_basts.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 2 THEN ajir_basts.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 3 THEN military_basts.name_' . app()->getLocale() . ' END) AS bast'),
                    DB::raw('(CASE WHEN employees.employee_type_id = 1 THEN employee_ranks.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 2 THEN ajir_ranks.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 3 THEN military_ranks.name_' . app()->getLocale() . ' END) AS rank'),
                    'provinces.name_' . app()->getLocale() . ' AS original_province',
                    'districts.name_' . app()->getLocale() . ' AS original_district',
                    'parent_departments.name_' . app()->getLocale() . ' AS parent_department_name',
                    'departments.name_' . app()->getLocale() . ' AS department_name',
                );

                // joins
                $query->leftjoin('departments AS parent_departments', 'parent_departments.id', '=', 'employees.parent_department_id');
                $query->leftjoin('departments AS departments', 'departments.id', '=', 'employees.department_id');
                $query->leftjoin('employee_types', 'employee_types.id', '=', 'employees.employee_type_id');
                $query->leftjoin('provinces', 'provinces.id', '=', 'employees.original_province_id');
                $query->leftjoin('districts', 'districts.id', '=', 'employees.original_district_id');
                $query->leftjoin('employee_ranks', 'employee_ranks.id', '=', 'employees.employee_rank_id');
                $query->leftjoin('employee_basts', 'employee_basts.id', '=', 'employees.employee_bast_id');
                $query->leftjoin('ajir_ranks', 'ajir_ranks.id', '=', 'employees.ajir_rank_id');
                $query->leftjoin('ajir_basts', 'ajir_basts.id', '=', 'employees.ajir_bast_id');
                $query->leftjoin('military_ranks', 'military_ranks.id', '=', 'employees.military_rank_id');
                $query->leftjoin('military_basts', 'military_basts.id', '=', 'employees.military_bast_id');
            }


           $query->orderByRaw('employees.employee_mawqif_id IS NULL, employees.employee_mawqif_id ASC')->orderBy('employees.employee_bast_id', 'DESC')->orderBy('employees.created_at', 'DESC');
         
            // return ->orderBy('employees.employee_bast_id', 'DESC')->orderBy('employees.created_at', 'DESC')
            return $this->applyDepartmentFilterRowQuery($query);
        } catch (\Exception $th) {
            //throw $th;
            dd($th);
        }
    }

    /**
     * Build continues absent employees query
     * @param employee_type has three values separatly or in array: 1: Employee, 2: Ajir, 3: Military
     * @param fetch_mode has three values: 'data', 'excel_report', 'count'
     */
    private function buildContinuesAbsentEmployeesQuery($fetch_mode, $read_status, $request = null, $employee_type = null)
    {
        $query = FacadesDB::table('attendance_notify_fire AS attendance_notify_fire');

        if (!is_null($request)) {
            $id = $request->id;
            $name = $request->name;
            $parent_department = $request->parent_department != 0 ? decrypt($request->parent_department) : 0;
            $department = $request->department != 0 ? decrypt($request->department) : 0;
            $current_status = $request->current_status;
            $gender = $request->gender;

            // read status
            if (!is_null($read_status)) {
                if (is_array($read_status) && count($read_status) > 0) {
                    $query->whereIn('attendance_notify_fire.mark_read', $read_status);
                } else {
                    $query->where('attendance_notify_fire.mark_read', $read_status);
                }
            }

            // query for id of employee
            if (!is_null($id) && $id != '') {
                $query = $query->where('employees.id', $id);
            }

            // query for name, last name and father name
            if (!is_null($name) && $name != '') {
                $query = $query->where(function ($q) use ($name) {
                    $q->where('employees.name', 'like', '%' . $name . '%')
                        ->orWhere('employees.name_en', 'like', '%' . $name . '%')
                        ->orWhere('employees.last_name', 'like', '%' . $name . '%')
                        ->orWhere('employees.last_name_en', 'like', '%' . $name . '%')
                        ->orWhere('employees.father_name', 'like', '%' . $name . '%')
                        ->orWhere('employees.father_name_en', 'like', '%' . $name . '%');
                });
            }

            // parent_department
            if (!is_null($parent_department) && $parent_department != '' && $parent_department != 0) {
                $query = $query->where('employees.parent_department_id', $parent_department);
            }

            // department
            if (!is_null($department) && $department != '' && $department != 0) {
                $query = $query->where('employees.department_id', $department);
            }

            // employee current status
            if (!is_null($current_status) && $current_status != '' && $current_status != 0) {
                $query = $query->where('employees.current_status', $current_status);
            }

            // gender
            if (!is_null($gender) && count($gender) > 0) {
                $query = $query->whereIn('employees.gender', $gender);
            }
        }

        // employee type
        if (!is_null($employee_type)) {
            if (is_array($employee_type) && count($employee_type) > 0) {
                $query->whereIn('employees.employee_type_id', $employee_type);
            } else {
                $query->where('employees.employee_type_id', $employee_type);
            }
        }

        if ($fetch_mode == 'data') {
            // select columns for datatable
            $query->select(
                'employees.*',
                'departments.name_' . app()->getLocale() . ' AS department',
                'employee_types.name_' . app()->getLocale() . ' AS employee_type',
                'employee_statuses.name_' . app()->getLocale() . ' AS current_status_name',
            );
            // joins
            $query->leftjoin('employees', 'employees.id', '=', 'attendance_notify_fire.employee_id');
            $query->leftjoin('departments', 'departments.id', '=', 'employees.department_id');
            $query->leftjoin('employee_types', 'employee_types.id', '=', 'employees.employee_type_id');
            $query->leftjoin('employee_statuses', 'employee_statuses.id', '=', 'employees.current_status');
        } else if ($fetch_mode == 'excel_report') {
            // select columns for excel report
            $query->select(
                'employees.id AS employee_id',
                'employees.name AS employee_name',
                'employees.last_name AS employee_last_name',
                'employees.name_en AS employee_name_en',
                'employees.father_name AS father_name',
                'employees.grand_father_name AS grand_father_name',
                'employees.email AS email',
                'employees.phone AS phone',
                'employees.gender AS gender',
                'employees.hire_date AS hire_date',
                'employees.tanqis_date AS tanqis_date',
                'employees.tanqis_no AS tanqis_number',
                'employees.tanqis_type AS tanqis_type',
                'employees.tanqis_remarks AS tanqis_remarks',
                'employees.current_position_' . app()->getLocale() . ' AS current_position',
                'employee_types.name_' . app()->getLocale() . ' AS employee_type_name',
                DB::raw('(CASE WHEN employees.employee_type_id = 1 THEN employee_basts.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 2 THEN ajir_basts.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 3 THEN military_basts.name_' . app()->getLocale() . ' END) AS bast'),
                DB::raw('(CASE WHEN employees.employee_type_id = 1 THEN employee_ranks.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 2 THEN ajir_ranks.name_' . app()->getLocale() . ' WHEN employees.employee_type_id = 3 THEN military_ranks.name_' . app()->getLocale() . ' END) AS rank'),
                'provinces.name_' . app()->getLocale() . ' AS original_province',
                'districts.name_' . app()->getLocale() . ' AS original_district',
                'parent_departments.name_' . app()->getLocale() . ' AS parent_department_name',
                'departments.name_' . app()->getLocale() . ' AS department_name',
            );

            // joins
            $query->leftjoin('departments AS parent_departments', 'parent_departments.id', '=', 'employees.parent_department_id');
            $query->leftjoin('departments AS departments', 'departments.id', '=', 'employees.department_id');
            $query->leftjoin('employee_types', 'employee_types.id', '=', 'employees.employee_type_id');
            $query->leftjoin('provinces', 'provinces.id', '=', 'employees.original_province_id');
            $query->leftjoin('districts', 'districts.id', '=', 'employees.original_district_id');
            $query->leftjoin('employee_ranks', 'employee_ranks.id', '=', 'employees.employee_rank_id');
            $query->leftjoin('employee_basts', 'employee_basts.id', '=', 'employees.employee_bast_id');
            $query->leftjoin('ajir_ranks', 'ajir_ranks.id', '=', 'employees.ajir_rank_id');
            $query->leftjoin('ajir_basts', 'ajir_basts.id', '=', 'employees.ajir_bast_id');
            $query->leftjoin('military_ranks', 'military_ranks.id', '=', 'employees.military_rank_id');
            $query->leftjoin('military_basts', 'military_basts.id', '=', 'employees.military_bast_id');
        }

        // sort
        $query->orderBy('employees.created_at', 'DESC');

        // return
        return $query;
    }

    /**
     * Build employees query
     * @param fetch_mode has three values: 'data', 'excel_report', 'count'
     */
    private function buildAnnualLeavesQuery($fetch_mode, $request)
    {
        try {
            // destruct data from $request
            $parent_department = $request->parent_department;
            $departments = $request->departments;
            $id = $request->id;
            $first_name = $request->first_name;
            $last_name = $request->last_name;
            $father_name = $request->father_name;
            $employee_type = $request->employee_type;
            $employee_bast = $request->employee_bast;
            $employee_rank = $request->employee_rank;
            $current_status = $request->current_status;
            $gender = $request->gender;

            // build the query
            $query = FacadesDB::table('employees');

            // parent department
            if (!is_null($parent_department) && $parent_department != 0 && $parent_department != '') {
                $query = $query->where('employees.parent_department_id', decrypter($parent_department));
            }

            // departments
            if (!is_null($departments) && count($departments) > 0) {
                $query = $query->whereIn('employees.department_id', decrypter($departments));
            }

            // query for id of employee
            if (!is_null($id) && $id != '') {
                $query = $query->where('employees.id', $id);
            }

            // query for both name and name_en
            if (!is_null($first_name) && $first_name != '') {
                $query = $query->where(function ($q) use ($first_name) {
                    $q->where('employees.name', 'like', '%' . $first_name . '%')
                        ->orWhere('employees.name_en', 'like', '%' . $first_name . '%');
                });
            }

            // query for both last_name and last_name_en
            if (!is_null($last_name) && $last_name != '') {
                $query = $query->where(function ($q) use ($last_name) {
                    $q->where('employees.last_name', 'like', '%' . $last_name . '%')
                        ->orWhere('employees.last_name_en', 'like', '%' . $last_name . '%');
                });
            }

            // query for both father_name and father_name_en
            if (!is_null($father_name) && $father_name != '') {
                $query = $query->where(function ($q) use ($father_name) {
                    $q->where('employees.father_name', 'like', '%' . $father_name . '%')
                        ->orWhere('employees.father_name_en', 'like', '%' . $father_name . '%');
                });
            }

            // employee type
            if (!is_null($employee_type) && $employee_type != 0 && $employee_type != '') {
                $query = $query->where('employees.employee_type_id', $employee_type);

                // employee bast
                if (!is_null($employee_bast) && $employee_bast != 0 && $employee_bast != '') {
                    if ($employee_type == 1) { // mamor
                        $query = $query->where('employees.employee_bast_id', $employee_bast);
                    } else if ($employee_type == 2) { // ajir
                        $query = $query->where('employees.ajir_bast_id', $employee_bast);
                    } else if ($employee_type == 3) { // military
                        $query = $query->where('employees.military_bast_id', $employee_bast);
                    }
                }

                // employee rank
                if (!is_null($employee_rank) && $employee_rank != 0 && $employee_rank != '') {
                    if ($employee_type == 1) { // mamor
                        $query = $query->where('employees.employee_rank_id', $employee_rank);
                    } else if ($employee_type == 2) { // ajir
                        $query = $query->where('employees.ajir_rank_id', $employee_rank);
                    } else if ($employee_type == 3) { // military
                        $query = $query->where('employees.military_rank_id', $employee_rank);
                    }
                }
            }

            // employee current status
            if (!is_null($current_status) && $current_status != 0 && $current_status != '') {
                $query = $query->where('employees.current_status', $current_status);
            }

            // gender
            if (!is_null($gender) && $gender != 0 && $gender != '') {
                $query = $query->where('employees.gender', $gender);
            }

            if ($fetch_mode == 'data') {
                $query->select(
                    'employees.*',
                    'employees.current_position_' . app()->getLocale() . ' AS current_position',
                    'employees.gender AS gender',
                    'employee_types.name_' . app()->getLocale() . ' AS employee_type_name',
                    'employee_statuses.name_' . app()->getLocale() . ' AS current_status_name',
                    'parent_departments.name_' . app()->getLocale() . ' AS parent_department_name',
                    'departments.name_' . app()->getLocale() . ' AS department_name',
                );
            } else if ($fetch_mode == 'excel_report') {
                $query->select(
                    'employees.*',
                    'employees.current_position_' . app()->getLocale() . ' AS current_position',
                    'employees.gender AS gender',
                    'employee_types.name_' . app()->getLocale() . ' AS employee_type_name',
                    'employee_statuses.name_' . app()->getLocale() . ' AS current_status_name',
                    'parent_departments.name_' . app()->getLocale() . ' AS parent_department_name',
                    'departments.name_' . app()->getLocale() . ' AS department_name',
                );
            }

            // joins
            $query->leftjoin('employee_types', 'employee_types.id', '=', 'employees.employee_type_id');
            $query->leftjoin('employee_statuses', 'employee_statuses.id', '=', 'employees.current_status');
            $query->leftjoin('departments AS departments', 'departments.id', '=', 'employees.department_id');
            $query->leftjoin('departments AS parent_departments', 'parent_departments.id', '=', 'employees.parent_department_id');

            $query->orderBy('employees.department_id', 'DESC');
            $query->orderBy('employees.tainat_number');

            return $query;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Build employees query
     * @param fetch_mode has three values: 'data', 'excel_report', 'count'
     */
    private function buildAttendanceQuery($fetch_mode, $request)
    {
        try {
            // destruct data from $request
            $parent_department = $request->parent_department;
            $departments = $request->departments;
            $id = $request->id;
            $first_name = $request->first_name;
            $last_name = $request->last_name;
            $father_name = $request->father_name;
            $employee_type = $request->employee_type;
            $employee_bast = $request->employee_bast;
            $employee_rank = $request->employee_rank;
            $current_status = $request->current_status;
            $gender = $request->gender;
            $contractual = $request->contractual;

            // build the query
            $query = FacadesDB::table('employees');

            if (is_null($contractual) || !in_array($contractual, [1, 2])) {
                $query->leftJoin('departments AS departments', 'departments.id', '=', 'employees.department_id');
                // parent department
                if (!is_null($parent_department) && $parent_department != 0 && $parent_department != '') {
                    $query = $query->where('employees.parent_department_id', decrypter($parent_department));
                }
                // departments
                if (!is_null($departments) && count($departments) > 0) {
                    $query = $query->whereIn('employees.department_id', decrypter($departments));
                }
                // employee current status
                if (!is_null($current_status) && $current_status != 0 && $current_status != '') {
                    $query = $query->where('employees.current_status', $current_status);
                } else {
                    $query = $query->where('employees.current_status', 1); // by default get only barhal employees
                }
                // query for id of employee
                if (!is_null($id) && $id != '') {
                    $query = $query->where('employees.id', $id);
                }
                // query for both name and name_en
                if (!is_null($first_name) && $first_name != '') {
                    $query = $query->where(function ($q) use ($first_name) {
                        $q->where('employees.name', 'like', '%' . $first_name . '%')
                            ->orWhere('employees.name_en', 'like', '%' . $first_name . '%');
                    });
                }
                // query for both last_name and last_name_en
                if (!is_null($last_name) && $last_name != '') {
                    $query = $query->where(function ($q) use ($last_name) {
                        $q->where('employees.last_name', 'like', '%' . $last_name . '%')
                            ->orWhere('employees.last_name_en', 'like', '%' . $last_name . '%');
                    });
                }
                // query for both father_name and father_name_en
                if (!is_null($father_name) && $father_name != '') {
                    $query = $query->where(function ($q) use ($father_name) {
                        $q->where('employees.father_name', 'like', '%' . $father_name . '%')
                            ->orWhere('employees.father_name_en', 'like', '%' . $father_name . '%');
                    });
                }
                // employee type
                if (!is_null($employee_type) && $employee_type != 0 && $employee_type != '') {
                    $query = $query->where('employees.employee_type_id', $employee_type);
                    // employee bast
                    if (!is_null($employee_bast) && $employee_bast != 0 && $employee_bast != '') {
                        if ($employee_type == 1) { // mamor
                            $query = $query->where('employees.employee_bast_id', $employee_bast);
                        } else if ($employee_type == 2) { // ajir
                            $query = $query->where('employees.ajir_bast_id', $employee_bast);
                        } else if ($employee_type == 3) { // military
                            $query = $query->where('employees.military_bast_id', $employee_bast);
                        }
                    }
                    // employee rank
                    if (!is_null($employee_rank) && $employee_rank != 0 && $employee_rank != '') {
                        if ($employee_type == 1) { // mamor
                            $query = $query->where('employees.employee_rank_id', $employee_rank);
                        } else if ($employee_type == 2) { // ajir
                            $query = $query->where('employees.ajir_rank_id', $employee_rank);
                        } else if ($employee_type == 3) { // military
                            $query = $query->where('employees.military_rank_id', $employee_rank);
                        }
                    }
                }
                // gender
                if (!is_null($gender) && $gender != 0 && $gender != '') {
                    $query = $query->where('employees.gender', $gender);
                }

                if ($fetch_mode == 'data') {
                    $query->select(
                        'employees.*',
                        'employees.current_position_' . app()->getLocale() . ' AS current_position',
                        'departments.name_' . app()->getLocale() . ' AS department_name',
                    );
                } else if ($fetch_mode == 'excel_report') {
                    $query->select(
                        'employees.*',
                        'employees.current_position_' . app()->getLocale() . ' AS current_position',
                        'departments.name_' . app()->getLocale() . ' AS department_name',
                    );
                }
                $query->orderBy('employees.department_id', 'DESC');
                $query->orderBy('employees.tainat_number');
            } else {
                $query->leftJoin('contracts', 'contracts.employee_id', '=', 'employees.id')
                    ->leftJoin('contract_types', 'contract_types.id', '=', 'contracts.contract_type_id')
                    ->where('contracts.contract_status_id', 1)  // Only active contractors
                    ->where('contracts.contract_type_id', $contractual);
                // parent department
                if (!is_null($parent_department) && $parent_department != 0 && $parent_department != '') {
                    $query = $query->where('contracts.parent_department_id', decrypter($parent_department));
                }
                // departments
                if (!is_null($departments) && count($departments) > 0) {
                    $query = $query->whereIn('contracts.department_id', decrypter($departments));
                }
                // query for id of employee
                if (!is_null($id) && $id != '') {
                    $query = $query->where('employees.id', $id);
                }
                // query for both name and name_en
                if (!is_null($first_name) && $first_name != '') {
                    $query = $query->where(function ($q) use ($first_name) {
                        $q->where('employees.name', 'like', '%' . $first_name . '%')
                            ->orWhere('employees.name_en', 'like', '%' . $first_name . '%');
                    });
                }
                // query for both last_name and last_name_en
                if (!is_null($last_name) && $last_name != '') {
                    $query = $query->where(function ($q) use ($last_name) {
                        $q->where('employees.last_name', 'like', '%' . $last_name . '%')
                            ->orWhere('employees.last_name_en', 'like', '%' . $last_name . '%');
                    });
                }
                // query for both father_name and father_name_en
                if (!is_null($father_name) && $father_name != '') {
                    $query = $query->where(function ($q) use ($father_name) {
                        $q->where('employees.father_name', 'like', '%' . $father_name . '%')
                            ->orWhere('employees.father_name_en', 'like', '%' . $father_name . '%');
                    });
                }
                // gender
                if (!is_null($gender) && $gender != 0 && $gender != '') {
                    $query = $query->where('employees.gender', $gender);
                }

                if ($fetch_mode == 'data') {
                    $select = "employees.*, 
                        contracts.department_" . app()->getLocale() . " AS department_name,
                        contracts.department_id AS department_id, 
                        contracts.job_title_" . app()->getLocale() . " AS current_position";

                    $query->selectRaw($select);
                } else if ($fetch_mode == 'excel_report') {
                    $query->selectRaw("
                        employees.*, 
                        contracts.department_" . app()->getLocale() . " AS department_name,
                        contracts.department_id AS department_id,  
                        contracts.job_title_" . app()->getLocale() . " AS current_position");
                }
                $query->orderBy('contracts.department_id', 'DESC');
            }
            return $query;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Build e-attendance employees query
     * @param fetch_mode has three values: 'data', 'excel_report', 'count'
     */
    private function buildEAttendanceEmployeesQuery($fetch_mode, $request)
    {
        try {
            // destruct data from $request
            $parent_department = $request->parent_department;
            $departments = $request->departments;
            $id = $request->id;
            $first_name = $request->first_name;
            $last_name = $request->last_name;
            $father_name = $request->father_name;
            $employee_type = $request->employee_type;
            $employee_bast = $request->employee_bast;
            $employee_rank = $request->employee_rank;
            $current_status = $request->current_status;
            $gender = $request->gender;

            // build the query
            $query = FacadesDB::table('employees');

            // only barhal
            $query = $query->where('employees.current_status', 1);

            // check RFID
            $query = $query->whereNotNull('employees.rfid');

            // parent department
            if (!is_null($parent_department) && $parent_department != 0 && $parent_department != '') {
                $query = $query->where('employees.parent_department_id', decrypter($parent_department));
            }

            // departments
            if (!is_null($departments) && count($departments) > 0) {
                $query = $query->whereIn('employees.department_id', decrypter($departments));
            }

            // query for id of employee
            if (!is_null($id) && $id != '') {
                $query = $query->where('employees.id', $id);
            }

            // query for both name and name_en
            if (!is_null($first_name) && $first_name != '') {
                $query = $query->where(function ($q) use ($first_name) {
                    $q->where('employees.name', 'like', '%' . $first_name . '%')
                        ->orWhere('employees.name_en', 'like', '%' . $first_name . '%');
                });
            }

            // query for both last_name and last_name_en
            if (!is_null($last_name) && $last_name != '') {
                $query = $query->where(function ($q) use ($last_name) {
                    $q->where('employees.last_name', 'like', '%' . $last_name . '%')
                        ->orWhere('employees.last_name_en', 'like', '%' . $last_name . '%');
                });
            }

            // query for both father_name and father_name_en
            if (!is_null($father_name) && $father_name != '') {
                $query = $query->where(function ($q) use ($father_name) {
                    $q->where('employees.father_name', 'like', '%' . $father_name . '%')
                        ->orWhere('employees.father_name_en', 'like', '%' . $father_name . '%');
                });
            }

            // employee type
            if (!is_null($employee_type) && $employee_type != 0 && $employee_type != '') {
                $query = $query->where('employees.employee_type_id', $employee_type);

                // employee bast
                if (!is_null($employee_bast) && $employee_bast != 0 && $employee_bast != '') {
                    if ($employee_type == 1) { // mamor
                        $query = $query->where('employees.employee_bast_id', $employee_bast);
                    } else if ($employee_type == 2) { // ajir
                        $query = $query->where('employees.ajir_bast_id', $employee_bast);
                    } else if ($employee_type == 3) { // military
                        $query = $query->where('employees.military_bast_id', $employee_bast);
                    }
                }

                // employee rank
                if (!is_null($employee_rank) && $employee_rank != 0 && $employee_rank != '') {
                    if ($employee_type == 1) { // mamor
                        $query = $query->where('employees.employee_rank_id', $employee_rank);
                    } else if ($employee_type == 2) { // ajir
                        $query = $query->where('employees.ajir_rank_id', $employee_rank);
                    } else if ($employee_type == 3) { // military
                        $query = $query->where('employees.military_rank_id', $employee_rank);
                    }
                }
            }

            // employee current status
            if (!is_null($current_status) && $current_status != 0 && $current_status != '') {
                $query = $query->where('employees.current_status', $current_status);
            } else {
                $query = $query->where('employees.current_status', 1); // by default get only barhal employees
            }

            // gender
            if (!is_null($gender) && $gender != 0 && $gender != '') {
                $query = $query->where('employees.gender', $gender);
            }

            if ($fetch_mode == 'data') {
                $query->select(
                    'employees.*',
                    'employees.current_position_' . app()->getLocale() . ' AS current_position',
                    'departments.name_' . app()->getLocale() . ' AS department_name',
                );
            } else if ($fetch_mode == 'excel_report') {
                $query->select(
                    'employees.*',
                    'employees.current_position_' . app()->getLocale() . ' AS current_position',
                    'departments.name_' . app()->getLocale() . ' AS department_name',
                );
            }

            // joins
            $query->leftjoin('departments AS departments', 'departments.id', '=', 'employees.department_id');

            $query->orderBy('employees.department_id', 'DESC');
            $query->orderBy('employees.tainat_number');
            return $query;
        } catch (\Exception $ex) {
            throw $ex;
        }
    }

    /**
     * @Desc: it check if a user exist in the attendance_notify_fire table 
     */
    private function fireUserExist($empID)
    {
        $table = FacadesDB::table('attendance_notify_fire');
        $userExist = $table->where('employee_id', '=', $empID)->get();
        return $userExist;
    }
}
