<?php

namespace App\Http\Controllers\Attendance;

use App\Exports\Attendance\Leave\LeaveExcelReport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Attendance\Leave\EmployeeTakenLeaveDeleteRequest;
use App\Http\Requests\Attendance\Leave\EmployeeTakenLeaveStoreRequest;
use App\Http\Requests\Attendance\Leave\EmployeeTakenLeaveUpdateRequest;
use App\Http\Requests\Attendance\Leave\ExraEntertainment\ExtraEntertainmentLeaveStoreRequest;
use App\Http\Requests\Attendance\Leave\LeaveDeleteRequest;
use App\Http\Requests\Attendance\Leave\LeaveStoreRequest;
use App\Http\Requests\Attendance\Leave\LeaveUpdateRequest;
use App\Models\Attendance\Leave\ExtraEntertainmentLeave;
use App\Repositories\Attendance\Attendance\AttendanceRepository;
use App\Repositories\Attendance\Attendance\ImageRepository;
use App\Repositories\Attendance\Holiday\HolidayRepository;
use App\Repositories\Attendance\Leave\LeaveRepository;
use App\Repositories\MasterData\LeaveTypeRepository;
use App\Repositories\Recruitment\EmployeeAttachmentRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\Tashkilat\Department\DepartmentRepository;
use App\Traits\ResponseTrait;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;

/**
 * This controller is used to manage the employee tranings records
 */
class LeaveController extends Controller
{
    use ResponseTrait;

    // repositories
    private $leaveRepository;
    private $leaveTypeRepository;
    private $employeeRepository;
    private $departmentRepository;
    private $holidayRepository;
    private $attendanceRepository;
    private $imageRepository;
    private $employeeAttachmentRepository;

    // constructor
    public function __construct(LeaveRepository $leaveRepository, LeaveTypeRepository $leaveTypeRepository, HolidayRepository $holidayRepository, AttendanceRepository $attendanceRepository, ImageRepository $imageRepository, EmployeeRepository $employeeRepository, EmployeeAttachmentRepository $employeeAttachmentRepository, DepartmentRepository $departmentRepository)
    {
        $this->middleware('auth');
        $this->leaveRepository = $leaveRepository;
        $this->leaveTypeRepository = $leaveTypeRepository;
        $this->employeeRepository = $employeeRepository;
        $this->departmentRepository = $departmentRepository;
        $this->holidayRepository = $holidayRepository;
        $this->attendanceRepository = $attendanceRepository;
        $this->imageRepository = $imageRepository;
        $this->employeeAttachmentRepository = $employeeAttachmentRepository;
    }

    /**
     * Get one employee list of leaves records [رخصتی های یک کارمند]
     * @param  request contains employee_id param
     */
    public function searchLeaves(Request $request)
    {
        try {
            // get data from repository
            $empID = decrypter($request->employee_id);

            // Determine category based on request
            $data = $this->leaveRepository->getLeaves($empID, $request->year,  $request->categoryType, $request->type);

            // render the datatable
            $rendered_leaves = View::make('components.pages.attendance.attendance.employee_attendance.leaves.leaves_datatable', ['data' => $data])->render();
            $employee = $this->employeeRepository->getEmployeeById($empID);
            // get leaves total
            $totals = $this->leaveRepository->getLeavesTotal($data, $empID, $request->year);
            $rendered_leaves_total = View::make('components.pages.attendance.attendance.employee_attendance.leaves.leaves_total', ['leaves_totals' => $totals, 'emp' => $employee])->render();

            // send success response
            return $this->getSuccessResponse(['leaves_datatable' => $rendered_leaves, 'leaves_total' => $rendered_leaves_total], trans('attendance/att.employee_leaves_fetched'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.employee_leaves_fetch_failed'), null, $ex);
        }
    }

    /**
     * Export employee leaves to excel file
     * @param request
     */
    public function exportLeavesToExcel(Request $request)
    {
      
        try {
            if (canDo('attendance_leave_management_excel_report')) {
                $employee_id = decrypt($request->employee_id);

                // employee
                $employee = $this->employeeRepository->getEmployeeById($employee_id);

                if (!is_null($employee)) {
                    // department
                    $department = $this->departmentRepository->getDepartmentByid($employee->department_id);

                    // get data from employee repository
                    $data = $this->leaveRepository->getLeaves($employee_id, $request->year, $request->leaveTypeCategory);

                    // employee details
                    $employee_details = trans('general.name') . ': ' . $employee->full_name . ' - ' . trans('general.father_name') . ': ' . $employee->my_father_name . (!is_null($department) ? ' - ' . trans('general.department') . ': ' . $department->name : '');

                    // file name and excel file
                    $file_name = trans('attendance/att.leaves_export_to_excel_file_title', ['date' => getCurrentShamsiDate(), 'employee' => $employee->full_name]);
                    $excel = new LeaveExcelReport($data, $employee_details);

                    // store one copy of the excel file to default storage in server
                    Excel::store($excel, 'attendance/exports/leaves/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                    // send one copy of excel file to user requested the report
                    return Excel::download($excel, $file_name . '.xlsx');
                }
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', trans('attendance/att.leaves_excel_report_generation_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Get one leave [رخصتی کارمند]
     * @param request contains askari_id param
     */
    public function getLeaveById(Request $request)
    {
        try {
            // get the record from repository
            $askari = $this->leaveRepository->getLeaveById($request->askari_id);

            // return the askari
            if (!is_null($askari)) {
                return $this->getSuccessResponse($askari, trans('attendance/att.leave_fetched'));
            }

            // not found
            return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'), $ex);
        }
    }

    /**
     * Get one employee list of extra leaves records [اضافه رخصتی های تفریحی یک کارمند]
     * @param  request contains employee_id param
     */
    public function searchExtraEntertainmentLeaves(Request $request)
    {
        try {
            // get data from repository
            $data = $this->leaveRepository->getExtraEntertainmentLeaves(decrypt($request->employee_id));

            // render the datatable
            $rendered_leaves = View::make('components.pages.attendance.attendance.employee_attendance.leaves.extra_entertainment_leaves_datatable', ['data' => $data])->render();

            // send success response
            return $this->getSuccessResponse(['leaves_datatable' => $rendered_leaves], trans('attendance/att.employee_leaves_fetched'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.employee_leaves_fetch_failed'), null, $ex);
        }
    }

    /**
     * Create new leave record for employee
     * The request is validated in use LeaveStoreRequest
     * @param  request contains employee_id and all the data param
     */
    public function store(LeaveStoreRequest $request)
    {
        try {
           
            $employee_id = $request->employee_id;
            $leave_type = $request->leave_type;
            $start_date = $request->leave_start_date;
            $end_date = $request->leave_end_date;

            // employee
            $employee = $this->employeeRepository->getEmployeeById($employee_id);
            $userLeave = $this->leaveTypeRepository->userLeaveCreateTypes();

            if (!in_array($request->leave_type, $userLeave)) { // user have permission to create leave
                return notAllowed([], $request);
            }
            // count number of days between the selected dates
            $requested_leave_days = (int)floor((strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24)) + 1;
            // ONE DAY LEAVE
            if ($requested_leave_days == 1) {
                // get the week day
                $days_of_week = date('l', strtotime($start_date));

                // leave at firday is not allowed
                if ($days_of_week == 'Friday') {
                    return $this->getErrorResponse(null, trans('attendance/att.leave_request_at_firday', ['date' => dateTo($start_date, 'shamsi', false)]), 'leave_request_at_firday');
                } else {
                    // check if this day is a general holiday
                    $holidays = $this->holidayRepository->getHolidaysByDate($start_date);

                    // if this day is holiday, then leave at holiday is not allowed
                    if (!is_null($holidays) && count($holidays) > 0 && $holidays->first()->holiday_type_id == 1) {
                        return $this->getErrorResponse(null, trans('attendance/att.leave_request_at_holiday', ['date' => dateTo($start_date, 'shamsi', false)]), 'leave_request_at_holiday');
                    } else {
                        // check if already has a leave record at this day
                        $leaves = $this->leaveRepository->getLeavesByDate($start_date, $employee_id);

                        // $hasNonType9And10Leave = (!is_null($leaves) && count($leaves) > 0) ? $leaves->whereNotIn('leave_type_id', [9, 10])->count() > 0 : false;
                        // if (!is_null($leaves) && count($leaves) > 0 && ($hasNonType9And10Leave || in_array($leave_type, [9, 10]))) {

                        $hasConflictingLeave = false;

                        if (!is_null($leaves) && count($leaves) > 0) {
                            foreach ($leaves as $existingLeave) {
                                // If existing leave is type 1-8 or 11, reject ALL leave types (1-11)
                                if (!in_array($existingLeave->leave_type_id, [9, 10])) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                                // If requesting leave type 9, reject only if existing leave is also type 9
                                elseif ($leave_type == 9 && $existingLeave->leave_type_id == 9) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                                // If requesting leave type 10, reject only if existing leave is also type 10
                                elseif ($leave_type == 10 && $existingLeave->leave_type_id == 10) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                            }
                        }
                        // if there's a conflicting leave, then multiple leaves at one day is not allowed
                        if ($hasConflictingLeave) {
                            return $this->getErrorResponse(null, trans('attendance/att.mutiple_leaves_at_same_date', ['date' => dateTo($start_date, 'shamsi', false)]), 'mutiple_leaves_at_same_date');
                        } else {
                            $previous_total_leaves = $this->leaveRepository->getTotalTakenLeaveByType($leave_type, $employee_id);
                            if ($leave_type == 1) { // Necessary
                                if ($requested_leave_days > 10) {
                                    return $this->getErrorResponse(null, trans('attendance/att.necessary_leave_is_only_10_days'), 'necessary_leave_is_only_10_days');
                                } else {
                                    if (($previous_total_leaves + $requested_leave_days) > 10) {
                                        $remainder = 10 - $previous_total_leaves;
                                        return $this->getErrorResponse(null, trans('attendance/att.necessary_leaves_exceed', ['count' => $remainder]), 'necessary_leaves_exceed');
                                    }
                                }
                            } else if ($leave_type == 2) { // Entertainment
                                if ($requested_leave_days > 20) {
                                    return $this->getErrorResponse(null, trans('attendance/att.entertainment_leave_is_only_20_days'), 'entertainment_leave_is_only_20_days');
                                } else {
                                    // Employee all government khedmat period should be >= 330 to be eligible for enterainment leave
                                    $khedmat_days = getEmployeeKhedmatPeriod($employee_id, true);
                                    if ($khedmat_days >= 330) {
                                        if (($previous_total_leaves + $requested_leave_days) > 20) {
                                            $remainder = 20 - $previous_total_leaves;
                                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                                        }
                                    } else {
                                        return $this->getErrorResponse(null, trans('attendance/att.employee_is_not_eligible_yet_for_entertainment_leave'), 'employee_is_not_eligible_yet_for_entertainment_leave');
                                    }
                                }
                            } else if ($leave_type == 3) { // Sick
                                if ($requested_leave_days > 20) {
                                    return $this->getErrorResponse(null, trans('attendance/att.sick_leave_is_only_20_days'), 'sick_leave_is_only_20_days');
                                } else {
                                    if (($previous_total_leaves + $requested_leave_days) > 20) {
                                        $remainder = 20 - $previous_total_leaves;
                                        return $this->getErrorResponse(null, trans('attendance/att.sick_leaves_exceed', ['count' => $remainder]), 'sick_leaves_exceed');
                                    }
                                }
                            } else if ($leave_type == 6) { // Hajj
                                if ($requested_leave_days > 45) {
                                    return $this->getErrorResponse(null, trans('attendance/att.hajj_leave_is_only_45_days'), 'hajj_leave_is_only_45_days');
                                } else {
                                    if (($previous_total_leaves + $requested_leave_days) > 45) {
                                        $remainder = 45 - $previous_total_leaves;
                                        return $this->getErrorResponse(null, trans('attendance/att.hajj_leaves_exceed', ['count' => $remainder]), 'hajj_leaves_exceed');
                                    }
                                }
                            }

                            if ($leave_type == 9) { // other issue
                                $selected_dates = $this->getSelectedDaysDates($start_date, $end_date, $request->other_issues_leave_selected_days);
                                // if days are selected
                                if ($selected_dates != false && !is_null($selected_dates) && count($selected_dates) > 0) {
                                    // upload attachment
                                    $url = null;
                                    if ($request->hasFile('leave_attachment')) {
                                        $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id);
                                    }

                                    foreach ($selected_dates as $item) {
                                        // SAVE LEAVE
                                        $request->merge([
                                            'leave_start_date' => $item,
                                            'leave_end_date' => $item
                                        ]);
                                        $saved_leave = $this->leaveRepository->store($request);
                                        $saved_leave->attachment_url = $url;
                                        $saved_leave->save();
                                    }
                                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                } else {
                                    // SAVE LEAVE
                                    $saved_leave = $this->leaveRepository->store($request);
                                    if ($saved_leave != false) {
                                        // upload attachment
                                        if ($request->hasFile('leave_attachment')) {
                                            $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                            $saved_leave->attachment_url = $url;
                                            $saved_leave->save();
                                        }
                                        return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                    }
                                }
                            } else if ($leave_type == 11) {
                                $extra_entertainment_leaves = ExtraEntertainmentLeave::where('employee_id', $employee_id)->where('to_year', getCurrentShamsiYear())->get();
                                if (!is_null($extra_entertainment_leaves) && count($extra_entertainment_leaves) > 0) {
                                    if ($requested_leave_days > 20) {
                                        return $this->getErrorResponse(null, trans('attendance/att.extra_entertainment_leave_is_only_20_days'), 'extra_entertainment_leave_is_only_20_days');
                                    } else {
                                        $extra = $extra_entertainment_leaves->first();
                                        if (($previous_total_leaves + $requested_leave_days) > $extra->days_no) {
                                            $remainder = $extra->days_no - $previous_total_leaves;
                                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                                        } else {
                                            // SAVE LEAVE
                                            $saved_leave = $this->leaveRepository->store($request);
                                            if ($saved_leave != false) {
                                                // save extra entertainment id in leave
                                                $saved_leave->extra_entertainment_leave_id = $extra->id;
                                                $saved_leave->save();

                                                // upload attachment
                                                if ($request->hasFile('leave_attachment')) {
                                                    $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                                    $saved_leave->attachment_url = $url;
                                                    $saved_leave->save();
                                                }
                                                return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                            }
                                        }
                                    }
                                } else {
                                    return $this->getErrorResponse(null, trans('attendance/att.employee_is_not_eligible_for_extra_entertainment_leave'), 'employee_is_not_eligible_for_extra_entertainment_leave');
                                }
                            } else {
                                // SAVE LEAVE
                                $saved_leave = $this->leaveRepository->store($request);
                                if ($saved_leave != false) {
                                    // upload attachment
                                    if ($request->hasFile('leave_attachment')) {
                                        $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                        $saved_leave->attachment_url = $url;
                                        $saved_leave->save();
                                    }
                                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                }
                            }
                        }
                    }
                }
            } else {
                // check if start_date is firday
                // get the week day
                $days_of_week = date('l', strtotime($start_date));

                // start day of the leave should not be friday
                if ($days_of_week == 'Friday') {
                    return $this->getErrorResponse(null, trans('attendance/att.start_date_is_friday', ['date' => dateTo($start_date, 'shamsi', false)]), 'start_date_is_friday');
                } else {
                    // check if end_date is firday
                    // get the week day
                    $days_of_week = date('l', strtotime($end_date));

                    // end day of the leave should not be friday
                    if ($days_of_week == 'Friday') {
                        return $this->getErrorResponse(null, trans('attendance/att.end_date_is_friday', ['date' => dateTo($start_date, 'shamsi', false)]), 'end_date_is_friday');
                    } else {
                        // check if between the selected days, a day is already leave
                        $leaves = $this->leaveRepository->getLeavesBetweenDates($start_date, $end_date, $employee_id);

                        $hasConflictingLeave = false;

                        if (!is_null($leaves) && count($leaves) > 0) {
                            foreach ($leaves as $existingLeave) {
                                // If existing leave is type 1-8 or 11, reject only if requesting same category (not 9 or 10)
                                if (!in_array($existingLeave->leave_type_id, [9, 10]) && !in_array($leave_type, [9, 10])) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                                // If requesting leave type 9, reject only if existing leave is also type 9
                                elseif ($leave_type == 9 && $existingLeave->leave_type_id == 9) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                                // If requesting leave type 10, reject only if existing leave is also type 10
                                elseif ($leave_type == 10 && $existingLeave->leave_type_id == 10) {
                                    $hasConflictingLeave = true;
                                    break;
                                }
                            }
                        }
                        // if there's a conflicting leave, then multiple leaves at one day is not allowed
                        if ($hasConflictingLeave) {
                            return $this->getErrorResponse(null, trans('attendance/att.a_day_between_the_selected_days_is_already_leave', ['date' => dateTo($start_date, 'shamsi', false)]), 'a_day_between_the_selected_days_is_already_leave');
                        } else {
                            // if the leave type not other issues, then check if between the selected days, employee has a present record
                            if ($leave_type != 9 && $this->imageRepository->isPresentBetweenDates($start_date, $end_date, $employee_id)) {
                                return $this->getErrorResponse(null, trans('attendance/att.a_day_between_the_selected_days_employee_is_present', ['date' => dateTo($start_date, 'shamsi', false)]), 'a_day_between_the_selected_days_employee_is_present');
                            } else {
                                $previous_total_leaves = $this->leaveRepository->getTotalTakenLeaveByType($leave_type, $employee_id);

                                if ($leave_type == 1) { // Necessary
                                    if ($requested_leave_days > 10) {
                                        return $this->getErrorResponse(null, trans('attendance/att.necessary_leave_is_only_10_days'), 'necessary_leave_is_only_10_days');
                                    } else {
                                        if (($previous_total_leaves + $requested_leave_days) > 10) {
                                            $remainder = 10 - $previous_total_leaves;
                                            return $this->getErrorResponse(null, trans('attendance/att.necessary_leaves_exceed', ['count' => $remainder]), 'necessary_leaves_exceed');
                                        }
                                    }
                                } else if ($leave_type == 2) { // Entertainment
                                    if ($requested_leave_days > 20) {
                                        return $this->getErrorResponse(null, trans('attendance/att.entertainment_leave_is_only_20_days'), 'entertainment_leave_is_only_20_days');
                                    } else {
                                        // Employee all government khedmat period should be >= 330 to be eligible for enterainment leave
                                        $khedmat_days = getEmployeeKhedmatPeriod($employee_id, true);
                                        if ($khedmat_days >= 330) {
                                            if (($previous_total_leaves + $requested_leave_days) > 20) {
                                                $remainder = 20 - $previous_total_leaves;
                                                return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                                            }
                                        } else {
                                            return $this->getErrorResponse(null, trans('attendance/att.employee_is_not_eligible_yet_for_entertainment_leave'), 'employee_is_not_eligible_yet_for_entertainment_leave');
                                        }
                                    }
                                } else if ($leave_type == 3) { // Sick
                                    if ($requested_leave_days > 20) {
                                        return $this->getErrorResponse(null, trans('attendance/att.sick_leave_is_only_20_days'), 'sick_leave_is_only_20_days');
                                    } else {
                                        if (($previous_total_leaves + $requested_leave_days) > 20) {
                                            $remainder = 20 - $previous_total_leaves;
                                            return $this->getErrorResponse(null, trans('attendance/att.sick_leaves_exceed', ['count' => $remainder]), 'sick_leaves_exceed');
                                        }
                                    }
                                } else if ($leave_type == 6) { // Hajj
                                    if ($requested_leave_days > 45) {
                                        return $this->getErrorResponse(null, trans('attendance/att.hajj_leave_is_only_45_days'), 'hajj_leave_is_only_45_days');
                                    } else {
                                        if (($previous_total_leaves + $requested_leave_days) > 45) {
                                            $remainder = 45 - $previous_total_leaves;
                                            return $this->getErrorResponse(null, trans('attendance/att.hajj_leaves_exceed', ['count' => $remainder]), 'hajj_leaves_exceed');
                                        }
                                    }
                                }

                                if ($leave_type == 9) { // other issue
                                    $selected_dates = $this->getSelectedDaysDates($start_date, $end_date, $request->other_issues_leave_selected_days);
                                    // if days are selected
                                    if ($selected_dates != false && !is_null($selected_dates) && count($selected_dates) > 0) {
                                        // upload attachment
                                        $url = null;
                                        if ($request->hasFile('leave_attachment')) {
                                            $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id);
                                        }

                                        foreach ($selected_dates as $item) {
                                            // SAVE LEAVE
                                            $request->merge([
                                                'leave_start_date' => $item,
                                                'leave_end_date' => $item
                                            ]);
                                            $saved_leave = $this->leaveRepository->store($request);
                                            $saved_leave->attachment_url = $url;
                                            $saved_leave->save();
                                        }
                                        return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                    } else {
                                        // SAVE LEAVE
                                        $saved_leave = $this->leaveRepository->store($request);
                                        if ($saved_leave != false) {
                                            // upload attachment
                                            if ($request->hasFile('leave_attachment')) {
                                                $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                                $saved_leave->attachment_url = $url;
                                                $saved_leave->save();
                                            }
                                            return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                        }
                                    }
                                } else if ($leave_type == 11) { // extra entertainment leave from previous year
                                    $extra_entertainment_leaves = ExtraEntertainmentLeave::where('employee_id', $employee_id)->where('to_year', getCurrentShamsiYear())->get();
                                    if (!is_null($extra_entertainment_leaves) && count($extra_entertainment_leaves) > 0) {
                                        if ($requested_leave_days > 20) {
                                            return $this->getErrorResponse(null, trans('attendance/att.extra_entertainment_leave_is_only_20_days'), 'extra_entertainment_leave_is_only_20_days');
                                        } else {
                                            $extra = $extra_entertainment_leaves->first();
                                            if (($previous_total_leaves + $requested_leave_days) > $extra->days_no) {
                                                $remainder = $extra->days_no - $previous_total_leaves;
                                                return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                                            } else {
                                                // SAVE LEAVE
                                                $saved_leave = $this->leaveRepository->store($request);
                                                if ($saved_leave != false) {
                                                    // save extra entertainment id in leave
                                                    $saved_leave->extra_entertainment_leave_id = $extra->id;
                                                    $saved_leave->save();

                                                    // upload attachment
                                                    if ($request->hasFile('leave_attachment')) {
                                                        $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                                        $saved_leave->attachment_url = $url;
                                                        $saved_leave->save();
                                                    }
                                                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                                }
                                            }
                                        }
                                    } else {
                                        return $this->getErrorResponse(null, trans('attendance/att.employee_is_not_eligible_for_extra_entertainment_leave'), 'employee_is_not_eligible_for_extra_entertainment_leave');
                                    }
                                } else {
                                    // SAVE LEAVE
                                    $saved_leave = $this->leaveRepository->store($request);
                                    if ($saved_leave != false) {
                                        // upload attachment
                                        if ($request->hasFile('leave_attachment')) {
                                            $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $saved_leave->id);
                                            $saved_leave->attachment_url = $url;
                                            $saved_leave->save();
                                        }
                                        return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                                    }
                                }

                                if ($leave_type == 10) { // included in attendance book
                                    // null rfid

                                }
                            }
                        }
                    }
                }
            }
            return $this->getErrorResponse(null, trans('general.something_went_wrong'), null);
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_create_failed'), null, $ex);
        }
    }

    /**
     * Transfer entertainment from one year to another year
     * @param request
     */
    public function storeExtraEntertainmentLeave(ExtraEntertainmentLeaveStoreRequest $request)
    {

        try {
            if (canDo('attendance-registration_leave_transfer')) {
                $employee_id = $request->employee_id;
                $current_year = (int)getCurrentShamsiYear();
                $from_year = $request->extra_from_year;
                $to_year = $request->extra_to_year;
                $day_no = (int)$request->extra_day_no;

                if ($from_year - $current_year == 0 || $from_year - $current_year == -1 || $day_no == 20) {
                    if ($to_year - $current_year == 0 || $to_year - $current_year == 1) {
                        $is_correct = ($to_year - $from_year) == 1 ? true : (($to_year - $from_year) == 2 ? false : ($from_year == $to_year ? false : (true)));
                    }
                }

                if ($is_correct == false) {
                    return $this->getErrorResponse(null, trans('attendance/att.incorrect_years_selected'), 'incorrect_years_selected');
                } else {
                    // check if already the leaves are transfered for the from_year to to_year
                    $already_transfered = $this->leaveRepository->checkIfEntertainmentLeavesAlreadyTransfered($from_year, $employee_id);
                    if ($already_transfered == true) {
                        return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_already_transfered'), 'entertainment_leaves_already_transfered');
                    } else {
                        // check if the employee has used the entertainment leaves [at least one day] at the from_year
                        $entertainment_leaves_totals = $this->leaveRepository->getTotalLeavesByType(2, $employee_id, $from_year);

                        if ($entertainment_leaves_totals > 0) {
                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_failed_to_transfer_because_employee_has_used_his_leaves'), 'entertainment_leaves_failed_to_transfer_because_employee_has_used_his_leaves');
                        } else {
                            $stored = $this->leaveRepository->storeExtraEntertainmentLeave($request);
                            if ($stored != false) {
                                return $this->getSuccessResponse(null, trans('attendance/att.extra_entertainment_leaves_transfered'));
                            }
                        }
                    }
                }
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_fetch_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Get one record of employee leave [رخصتی]
     * @param  request contains leave id param
     */
    public function showLeaveEditForm(Request $request, $id)
    {
        try {
            if (canDo('attendance-leave_edit')) {
                // Get the record from repository
                $leave = $this->leaveRepository->getLeaveById($id);

                if (is_null($leave)) {
                    return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'));
                }

                $leaveTypeCategory = $request->leaveTypeCategory;
                // Fetch leave types based on the category
                $data['leave'] = $leave;
                $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes($leaveTypeCategory);
                $data['user_allowed_leave_types'] = $this->leaveTypeRepository->userLeaveEditTypes($leaveTypeCategory);

                // Render the component
                $rendered_leave = View::make('components.pages.attendance.attendance.employee_attendance.leaves.edit_modal', ['data' => $data])->render();

                // Send success response
                return $this->getSuccessResponse(['rendered_leave' => $rendered_leave, 'leave' => $data['leave']], trans('attendance/att.leave_fetched'));
            }

            // Return not allowed response
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_fetch_failed'), null, $ex);
        }
    }

    public function showLeaveCreateForm(Request $request)
    {
        try {
            if (canDo('attendance-leave_registration')) {
                // employee
                $data['employee'] = $this->employeeRepository->getEmployeeById($request->id);
                // leaveType category
                $leaveTypeCategory = $request->leaveTypeCategory;
                // Determine leaveTypeCategory based on request
                $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes($leaveTypeCategory);
                if ($leaveTypeCategory == config('lists.attendance.leaveTypesCategory')[1]) {
                    // Render the component
                    $rendered_leave = View::make('components.pages.attendance.attendance.employee_attendance.leaves.create_modal', ['data' => $data])->render();
                } else {
                    $rendered_leave = View::make('components.pages.attendance.attendance.employee_attendance.other_issues.create_modal', ['data' => $data])->render();
                }


                // Send success response
                return $this->getSuccessResponse(['rendered_leave' => $rendered_leave], trans('attendance/att.leave_fetched'));
            }

            // Return not allowed response
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_create_failed'), null, $ex);
        }
    }

    /**
     * Update leave record
     * The request is validated in use LeaveUpdateRequest
     * @param  request contains leave_id and all the data param
     */
    public function update(LeaveUpdateRequest $request)
    {
        try {
            $employee_id = $request->employee_id;
            $leave_id = $request->leave_id;
            $leave_type = $request->leave_type_edit;
            $start_date = $request->leave_start_date_edit;
            $end_date = $request->leave_end_date_edit;
            $leave_type_category = $request->leave_type_category;

            // leave
            $leave = $this->leaveRepository->getLeaveById($leave_id);
            $userLeave = $this->leaveTypeRepository->userLeaveEditTypes($leave_type_category);
            if (!in_array($leave->leave_type_id, $userLeave) && !in_array($request->leave_type_edit, $userLeave)) { // user have permission to edit leave
                return notAllowed([], $request);
            }
            $employee = $this->employeeRepository->getEmployeeById($employee_id);

            if (!is_null($employee)) {
                if (!is_null($leave)) {
                    $requested_leave_days = (int)floor((strtotime($end_date) - strtotime($start_date)) / (60 * 60 * 24)) + 1;
                    $total_leaves = $this->leaveRepository->getTotalTakenLeaveByType($leave_type, $employee_id);
                    $current_leave_days = $leave->days_no;

                    if ($leave_type == $leave->leave_type_id) {
                        $new_leave_total = ($total_leaves - $current_leave_days) + $requested_leave_days;
                    } else {
                        $new_leave_total = $total_leaves + $requested_leave_days;
                    }

                    if ($leave_type == 1) { // Necessary
                        if ($requested_leave_days > 10) {
                            return $this->getErrorResponse(null, trans('attendance/att.necessary_leave_is_only_10_days'), 'necessary_leave_is_only_10_days');
                        } else {
                            if ($new_leave_total > 10) {
                                $remainder = 10 - $total_leaves;
                                return $this->getErrorResponse(null, trans('attendance/att.necessary_leaves_exceed', ['count' => $remainder]), 'necessary_leaves_exceed');
                            }
                        }
                    } else if ($leave_type == 2) { // Entertainment
                        if ($requested_leave_days > 20) {
                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leave_is_only_20_days'), 'entertainment_leave_is_only_20_days');
                        } else {
                            if ($new_leave_total > 20) {
                                $remainder = 20 - $total_leaves;
                                return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                            }
                        }
                    } else if ($leave_type == 3) { // Sick
                        if ($requested_leave_days > 20) {
                            return $this->getErrorResponse(null, trans('attendance/att.sick_leave_is_only_20_days'), 'sick_leave_is_only_20_days');
                        } else {
                            if ($new_leave_total > 20) {
                                $remainder = 20 - $total_leaves;
                                return $this->getErrorResponse(null, trans('attendance/att.sick_leaves_exceed', ['count' => $remainder]), 'sick_leaves_exceed');
                            }
                        }
                    } else if ($leave_type == 6) { // Hajj
                        if ($requested_leave_days > 45) {
                            return $this->getErrorResponse(null, trans('attendance/att.hajj_leave_is_only_45_days'), 'hajj_leave_is_only_45_days');
                        } else {
                            if ($new_leave_total > 45) {
                                $remainder = 45 - $total_leaves;
                                return $this->getErrorResponse(null, trans('attendance/att.hajj_leaves_exceed', ['count' => $remainder]), 'hajj_leaves_exceed');
                            }
                        }
                    } else if ($leave_type == 11) { // Extra entertainment leaves
                        if ($requested_leave_days > 20) {
                            return $this->getErrorResponse(null, trans('attendance/att.extra_entertainment_leave_is_only_20_days'), 'extra_entertainment_leave_is_only_20_days');
                        } else {
                            if ($new_leave_total > 20) {
                                $remainder = 20 - $total_leaves;
                                return $this->getErrorResponse(null, trans('attendance/att.extra_entertainment_leaves_exceed', ['count' => $remainder]), 'extra_entertainment_leaves_exceed');
                            }
                        }
                    }

                    $updated_leave = $this->leaveRepository->update($request, $leave);
                    if ($updated_leave != false) {
                        // upload attachment
                        if ($request->hasFile('leave_attachment_edit')) {
                            // delete previous attachment
                            $this->employeeAttachmentRepository->delete($updated_leave->attachment_url, $this->employeeAttachmentRepository->leave_attachment_type_id, $updated_leave->id);

                            // upload new attachment
                            $url = $this->employeeAttachmentRepository->upload($request->file('leave_attachment_edit'), $this->employeeAttachmentRepository->leave_attachment_type_id, $employee_id, $employee->department_id, $updated_leave->id);
                            $updated_leave->attachment_url = $url;
                            $updated_leave->save();
                        }
                        return $this->getSuccessResponse(null, trans('attendance/att.leave_record_updated'));
                    }
                }
                return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'));
            }
            return $this->getErrorResponse(null, trans('general.employee_not_found'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_update_failed'), $ex);
        }
    }

    /**
     * Delete leave record
     * The request is validated in use LeaveDeleteRequest
     * @param  request contains leave_id and all the data param
     */
    public function delete(LeaveDeleteRequest $request)
    {
        try {
            $leave = $this->leaveRepository->getLeaveById($request->leave_id);
            $userAllowedLeaves = $this->leaveTypeRepository->userLeaveDeleteTypes();
            if (in_array($leave->leave_type_id, $userAllowedLeaves)) {
                // delete leave 
                $deleted = $this->leaveRepository->delete($request->leave_id);
                // deleted successfully leave record
                if ($deleted != false) {
                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_deleted'));
                }
            }

            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_delete_failed'), $ex);

            //throw $ex;
        }
    }

    public function showTakenLeaveCreateForm(Request $request)
    {
        try {
            if (canDo('attendance-registration_of_previous_departments_leaves')) {
                // employee
                $data['employee'] = $this->employeeRepository->getEmployeeById($request->id);
                // leave types
                $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes();
                // Render the component
                $rendered_leave = View::make('components.pages.attendance.attendance.employee_attendance.employee_taken_leaves.create_modal', ['data' => $data])->render();
                // Send success response
                return $this->getSuccessResponse(['rendered_leave' => $rendered_leave], trans('attendance/att.leave_fetched'));
            }
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_create_failed'), null, $ex);
        }
    }

    public function showEmployeeTakenLeaveEditForm(Request $request, $id)
    {
        try {
            if (canDo('attendance-edit_previous_departments_leaves')) {
                // get the record from db
                $takenLeave = $this->leaveRepository->getTakenLeaveById(decrypt($id));
                if (is_null($takenLeave)) {
                    return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'));
                }

                $data['taken_leave'] = $takenLeave;
                $data['leave_types'] = $this->leaveTypeRepository->getAllLeaveTypes();

                // Render the component
                $rendered_leave = View::make('components.pages.attendance.attendance.employee_attendance.employee_taken_leaves.edit_modal', ['data' => $data])->render();

                // Send success response
                return $this->getSuccessResponse(['rendered_leave' => $rendered_leave, 'taken_leave' => $data['taken_leave']], trans('attendance/att.leave_fetched'));
            }
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_fetch_failed'), null, $ex);
        }
    }

    public function storeEmployeeTakenLeave(EmployeeTakenLeaveStoreRequest $request)
    {
        try {
            if (canDo('attendance-registration_of_previous_departments_leaves')) {
                $employee_id = $request->employee_id;
                $taken_leave_type = $request->taken_leave_type;
                $taken_leave_year = $request->taken_leave_year;
                $requested_taken_leave_days = $request->taken_leave_days;
                $previous_taken_total_leave = $this->leaveRepository->getTotalTakenLeaveByType($taken_leave_type, $employee_id, $taken_leave_year);
                if ($taken_leave_type == 1) { // Necessary
                    if ($requested_taken_leave_days > 10) {
                        return $this->getErrorResponse(null, trans('attendance/att.necessary_leave_is_only_10_days'), 'necessary_leave_is_only_10_days');
                    } else {
                        if (($previous_taken_total_leave + $requested_taken_leave_days) > 10) {
                            $reminder = 10 - $previous_taken_total_leave;
                            return $this->getErrorResponse(null, trans('attendance/att.necessary_leaves_exceed', ['count' => $reminder]), 'necessary_leaves_exceed');
                        }
                    }
                } else if ($taken_leave_type == 2) { // Entertainment
                    if ($requested_taken_leave_days > 20) {
                        return $this->getErrorResponse(null, trans('attendance/att.entertainment_leave_is_only_20_days'), 'entertainment_leave_is_only_20_days');
                    } else {
                        if (($previous_taken_total_leave + $requested_taken_leave_days) > 20) {
                            $remainder = 20 - $previous_taken_total_leave;
                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                        }
                    }
                } else if ($taken_leave_type == 3) { // Sick
                    if ($requested_taken_leave_days > 20) {
                        return $this->getErrorResponse(null, trans('attendance/att.sick_leave_is_only_20_days'), 'sick_leave_is_only_20_days');
                    } else {
                        if (($previous_taken_total_leave + $requested_taken_leave_days) > 20) {
                            $remainder = 20 - $previous_taken_total_leave;
                            return $this->getErrorResponse(null, trans('attendance/att.sick_leaves_exceed', ['count' => $remainder]), 'sick_leaves_exceed');
                        }
                    }
                } else if ($taken_leave_type == 6) {
                    if ($requested_taken_leave_days > 45) {
                        return $this->getErrorResponse(null, trans('attendance/att.hajj_leave_is_only_45_days'), 'hajj_leave_is_only_45_days');
                    } else {
                        if (($previous_taken_total_leave + $requested_taken_leave_days) > 45) {
                            $remainder = 45 - $previous_taken_total_leave;
                            return $this->getErrorResponse(null, trans('attendance/att.hajj_leaves_exceed', ['count' => $remainder]), 'hajj_leaves_exceed');
                        }
                    }
                }
                $saved_employee_taken_leaves = $this->leaveRepository->storeEmployeeTakenLeave($request);
                if ($saved_employee_taken_leaves != false) {
                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_created'));
                }
            }
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return $this->getErrorResponse(null, __('general.not_successful'), null, $ex);
        }
    }

    public function updateEmployeeTakenLeave(EmployeeTakenLeaveUpdateRequest $request)
    {
        try {
            if (canDo('attendance-edit_previous_departments_leaves')) {
                $employee_id = $request->employee_id;
                $taken_leave_id = $request->taken_leave_id;
                $taken_leave_type = $request->taken_leave_type_edit;
                $taken_leave_year = $request->taken_leave_year_edit;

                $employeeTakenLeave = $this->leaveRepository->getTakenLeaveById($taken_leave_id);


                if (!is_null($employeeTakenLeave)) {
                    $requested_taken_leave_days = $request->taken_leave_days_edit;
                    $previous_taken_total_leave = $this->leaveRepository->getTotalTakenLeaveByType($taken_leave_type, $employee_id, $taken_leave_year);
                    $current_taken_leave_days = $employeeTakenLeave->days_no;

                    if ($taken_leave_type == $employeeTakenLeave->leave_type_id) {
                        $new_taken_leave_total = ($previous_taken_total_leave - $current_taken_leave_days) + $requested_taken_leave_days;
                    } else {
                        $new_taken_leave_total = $previous_taken_total_leave + $requested_taken_leave_days;
                    }

                    if ($taken_leave_type == 1) { // Necessary
                        if ($requested_taken_leave_days > 10) {
                            return $this->getErrorResponse(null, trans('attendance/att.necessary_leave_is_only_10_days'), 'necessary_leave_is_only_10_days');
                        } else {
                            if ($new_taken_leave_total > 10) {
                                $remainder = 10 - $previous_taken_total_leave;
                                return $this->getErrorResponse(null, trans('attendance/att.necessary_leaves_exceed', ['count' => $remainder]), 'necessary_leaves_exceed');
                            }
                        }
                    } else if ($taken_leave_type == 2) { // Entertainment
                        if ($requested_taken_leave_days > 20) {
                            return $this->getErrorResponse(null, trans('attendance/att.entertainment_leave_is_only_20_days'), 'entertainment_leave_is_only_20_days');
                        } else {
                            if ($new_taken_leave_total > 20) {
                                $remainder = 20 - $previous_taken_total_leave;
                                return $this->getErrorResponse(null, trans('attendance/att.entertainment_leaves_exceed', ['count' => $remainder]), 'entertainment_leaves_exceed');
                            }
                        }
                    } else if ($taken_leave_type == 3) { // Sick
                        if ($requested_taken_leave_days > 20) {
                            return $this->getErrorResponse(null, trans('attendance/att.sick_leave_is_only_20_days'), 'sick_leave_is_only_20_days');
                        } else {
                            if ($new_taken_leave_total > 20) {
                                $remainder = 20 - $previous_taken_total_leave;
                                return $this->getErrorResponse(null, trans('attendance/att.sick_leaves_exceed', ['count' => $remainder]), 'sick_leaves_exceed');
                            }
                        }
                    } else if ($taken_leave_type == 6) { // Hajj
                        if ($requested_taken_leave_days > 45) {
                            return $this->getErrorResponse(null, trans('attendance/att.hajj_leave_is_only_45_days'), 'hajj_leave_is_only_45_days');
                        } else {
                            if ($new_taken_leave_total > 45) {
                                $remainder = 45 - $previous_taken_total_leave;
                                return $this->getErrorResponse(null, trans('attendance/att.hajj_leaves_exceed', ['count' => $remainder]), 'hajj_leaves_exceed');
                            }
                        }
                    }

                    $updated_taken_leave = $this->leaveRepository->updateEmployeeTakenLeave($request, $employeeTakenLeave);
                    if ($updated_taken_leave != false) {
                        return $this->getSuccessResponse(null, trans('attendance/att.leave_record_updated'));
                    }
                }
                return $this->getErrorResponse(null, trans('attendance/att.leave_not_found'));
            }
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_update_failed'), $ex);
        }
    }

    public function deleteEmployeeTakenLeave(EmployeeTakenLeaveDeleteRequest $request)
    {
        try {
            if (canDo('attendance-registration_leave_transfer')) {
                $deleted = $this->leaveRepository->deleteEmployeeTakenLeave($request->taken_leave_id);

                if ($deleted != false) {
                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_deleted'));
                }
            }
            return notAllowed([], $request);
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_delete_failed'), $ex);
        }
    }

    public function searchEmployeeTakenLeave(Request $request)
    {
        try {
            $empId = decrypter($request->employee_id);

            // get data from repository
            $takenLeave = $this->leaveRepository->getEmployeeTakenLeaves($empId, $request->year);
            $data = $this->leaveRepository->getLeaves($empId, $request->year);
            // render the datatable
            $taken_rendered_leaves = View::make('components.pages.attendance.attendance.employee_attendance.employee_taken_leaves.employee_taken_leaves_datatable', ['data' => $takenLeave])->render();
            $employee = $this->employeeRepository->getEmployeeById($empId);
            // get leaves total
            $totals = $this->leaveRepository->getLeavesTotal($data, $empId, $request->year);

            $rendered_leaves_total = View::make('components.pages.attendance.attendance.employee_attendance.leaves.leaves_total', ['leaves_totals' => $totals, 'emp' => $employee])->render();
            // send success response 
            return $this->getSuccessResponse(['leaves_taken_datatable' => $taken_rendered_leaves, 'leaves_total' => $rendered_leaves_total], trans('attendance/att.employee_leaves_fetched'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('attendance/att.employee_leaves_fetch_failed'), null, $ex);
        }
    }

    // HELPERS
    // this function is used in other issue leaves to convert the selected week days to dates
    private function getSelectedDaysDates($from_date, $to_date, $selected_days)
    {
        if (!is_null($selected_days) && count($selected_days) > 0) {
            $days = $this->getDaysBetweenDates($from_date, $to_date);
            $selected_dates = [];
            if (!is_null($days) && count($days) > 0) {
                foreach ($days as $day) {
                    if (in_array(date("w", strtotime($day)), $selected_days)) {
                        array_push($selected_dates, $day);
                    }
                }
            }
            $selected_dates = count($selected_dates) > 0 ? $selected_dates : false;
            return $selected_dates;
        }
        return false;
    }

    // this function is used in other issue leaves
    private function getDaysBetweenDates($from, $to)
    {
        $begin = new DateTime($from);
        $end = new DateTime($to . ' +1 day');
        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);
        $days = [];
        foreach ($period as $da) {
            array_push($days, $da->format('Y-m-d'));
        }
        return $days;
    }

    public function deleteExtraEntertainmentLeave(Request $request)
    {

        try {

            if ($request->ref && canDo('attendance-delete_leave_transfer')) {
                if ($this->leaveRepository->removeExtraLeave(decrypter($request->ref))) {
                    return $this->getSuccessResponse(null, trans('attendance/att.leave_record_deleted'));
                }
            }

            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('attendance/att.leave_record_delete_failed'), $ex);
            //throw $ex;
        }
    }
}
