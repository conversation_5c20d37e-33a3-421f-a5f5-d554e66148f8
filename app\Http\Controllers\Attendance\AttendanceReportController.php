<?php

namespace App\Http\Controllers\Attendance;

use App\Exports\Attendance\Report\EAttendanceEmployeesExcelReport;
use App\Exports\Attendance\Report\EmployeesAnnualLeavesExcelReport;
use App\Exports\Attendance\Report\EmployeesAttendanceExcelReport;
use App\Exports\Attendance\Report\EmployeesDailyAttendanceExcelReport;
use App\Exports\Attendance\Report\EmployeesSpecialDailyAttendanceExcelReport;
use App\Exports\Attendance\Report\EmployeesTimeBasedAttendanceExcelReport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Attendance\Report\AnnualLeaveReportRequest;
use App\Http\Requests\Attendance\Report\AttendanceReportRequest;
use App\Http\Requests\Attendance\Report\DailyAttendanceReportRequest;
use App\Http\Requests\Attendance\Report\EAttendanceEmployeesReportRequest;
use App\Http\Requests\Attendance\Report\TimeBasedAttendanceReportRequest;
use App\Repositories\Attendance\Attendance\AttendanceRepository;
use App\Repositories\Attendance\Holiday\HolidayRepository;
use App\Repositories\Attendance\Leave\LeaveRepository;
use App\Repositories\Attendance\Machines\MachinesRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\Tashkilat\Department\DepartmentRepository;
use App\Traits\ResponseTrait;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class AttendanceReportController extends Controller
{
    use ResponseTrait;

    private $attendanceRepository;
    private $departmentRepository;
    private $employeeRepository;
    private $leaveRepository;
    private $holidayRepository;
    private $machinesRepository;

    public function __construct(AttendanceRepository $attendanceRepository, LeaveRepository $leaveRepository, EmployeeRepository $employeeRepository, DepartmentRepository $departmentRepository, HolidayRepository $holidayRepository, MachinesRepository $machinesRepository)
    {
        $this->middleware('auth');
        $this->attendanceRepository = $attendanceRepository;
        $this->employeeRepository = $employeeRepository;
        $this->departmentRepository = $departmentRepository;
        $this->leaveRepository = $leaveRepository;
        $this->holidayRepository = $holidayRepository;
        $this->machinesRepository = $machinesRepository;
    }

    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | 1. START ANNUAL LEAVES REPORT راپور سالانه رخصتی
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */
    /**
     * Get annual leaves report page
     */
    public function getEmployeesAnnualLeavesReport(Request $request)
    {

        try {
            if (canDo('attendance-view_attendance_report')) {
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_statuses'] = $this->employeeRepository->getEmployeeStatuses();

                return view('pages.attendance.reports.annual_leaves', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'attendance.reports.annual-leaves.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            Log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employee for annual leaves report
     */
    public function searchEmployeesForAnnualLeavesReport(Request $request)
    {
        try {
            if (canDo('attendance-annual_holidays_report')) {
                $data = $this->attendanceRepository->searchEmployeesForAnnualLeavesReport($request, 'paginate');

                foreach ($data as $item) {
                    $all_leaves = $this->leaveRepository->getLeavesByYear($item->id, $request->year);
                    $totals = $this->leaveRepository->getLeavesTotal($all_leaves);
                    $item->totals = $totals;
                }

                return view('components.pages.attendance.reports.annual_leaves_datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.search_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Get employees annual leaves report
     */
    public function getEmployeesAnnualLeavesExcelReport(AnnualLeaveReportRequest $request)
    {

        try {
            if (canDo('attendance-annual_holidays_report')) {
                $data = $this->attendanceRepository->searchEmployeesForAnnualLeavesReport($request, 'all');

                foreach ($data as $item) {
                    $all_leaves = $this->leaveRepository->getLeavesByYear($item->id, $request->year);
                    $totals = $this->leaveRepository->getLeavesTotal($all_leaves);
                    $item->totals = $totals;
                }

                // file name and excel file
                $file_name = trans('attendance/att.employees_annual_leaves_export_to_excel_file_title', ['date' => getCurrentShamsiDate()]);
                $excel = new EmployeesAnnualLeavesExcelReport($data);

                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'attendance/exports/reports/annual_leaves/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));
            //throw $ex;
        }
    }
    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | END ANNUAL LEAVES REPORT راپور سالانه رخصتی
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */


    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | 2. START ATTENDANCE REPORT راپور حاضری
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */
    /**
     * Get attendance report page
     */
    public function getEmployeesAttendanceReport(Request $request)
    {
        try {
            if (canDo('attendance-att_report')) {
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_statuses'] = $this->employeeRepository->getEmployeeStatuses();

                return view('pages.attendance.reports.attendance', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'attendance.reports.attendance.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employees for attendance report
     */
    public function searchEmployeesForAttendanceReport(Request $request)
    {
        try {
            if (canDo('attendance-att_report')) {
                $data = $this->attendanceRepository->searchEmployeesReport($request, 'paginate');

                return view('components.pages.attendance.reports.attendance_datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.search_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Get employees attendance report
     */
    public function getEmployeesAttendanceExcelReport(AttendanceReportRequest $request)
    {


        try {
            if (canDo('attendance-att_report')) {
                $start = microtime(true);
                $date_type = $request->date_type;
                $month = $request->month;
                $year = $request->year;
                $start_date = null;
                $end_date = null;
                
                // set start_date and end_date
                // monthly
                if ($date_type == 1) {
                    if (!is_null($month) && $month != '' && $month != 0) {
                        // check if year is selected, if not then get current year
                        $the_year = !is_null($year) && $year != '' && $year != 0 ? $year : getCurrentShamsiYear();

                        // month first date
                        $start_date = dateTo(('01-' . $month . '-' . $the_year), 'miladi', false);

                        // month last date
                        if ($month >= 1 && $month <= 6) { // [حمل، ثور، جوزا، سرطان، اسد، سنبله]
                            $end_date = dateTo(('31-' . $month . '-' . $the_year), 'miladi', false);
                        } else if ($month == 12) { // [حوت]
                            $end_date = dateTo((isLeapYear($the_year) ? '30-' . $month . '-' . $the_year : '29-' . $month . '-' . $the_year), 'miladi', false);
                        } else { // [میزان، عقرب، قوس، جدی، دلو]
                            $end_date = dateTo(('30-' . $month . '-' . $the_year), 'miladi', false);
                        }
                    }
                } else {
                    $start_date = $request->start_date;
                    $end_date = $request->end_date;
                }

                // holidays
                $holidays = $this->holidayRepository->getHolidayDatesBetweenDates($start_date, $end_date);

                // employees
                $employees = $this->attendanceRepository->searchEmployeesReport($request, 'all');
                // dd($employees->department_name);
                foreach ($employees as $employee) {
                    // get leaves

                    $all_leaves = $this->leaveRepository->getLeavesBetweenDates($start_date, $end_date, $employee->id);

                    $employee->totals = $this->leaveRepository->getLeavesTotal($all_leaves);
                    $employee->leaves_dates = $this->leaveRepository->leavesToString($all_leaves);

                    // get images
                    $employee->images = $this->attendanceRepository->getEmployeeImagesBetweenDates($employee->id, $start_date, $end_date);

                    // get present days
                    $employeeAttendanceDays = $this->getEmployeeAttendanceDays($employee, $start_date, $end_date, $employee->images, $holidays, $all_leaves);
                    $employee->present_days = $employeeAttendanceDays['present'];

                    // get absent days
                     $employee->absent_days = $employeeAttendanceDays['absent'];
                }
                // file name
                if ($date_type == 1) {
                    $file_name = trans('attendance/att.employees_monthly_attendance_export_to_excel_file_title', ['year' => $year, 'month' => config('custom.shamsi.months.' . app()->getLocale() . '.' . $month), 'date' => getCurrentShamsiDate()]);
                } else {
                    $file_name = trans('attendance/att.employees_choise_date_attendance_export_to_excel_file_title', ['start_date' => dateTo($start_date, 'shamsi', false), 'end_date' => dateTo($end_date, 'shamsi', false), 'date' => getCurrentShamsiDate()]);
                }
               
                $excel = new EmployeesAttendanceExcelReport($employees, $file_name);
             
              
                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'attendance/exports/reports/attendance/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }
    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | END ATTENDANCE REPORT راپور حاضری
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */


    // HELPERS
    private function getEmployeePresentDays($employee, $start_date, $end_date, $images, $holidays, $leaves)
    {
        try {
            $present_days = array();

            if (!is_null($employee->attendance_in_date) && $start_date < $employee->attendance_in_date) {
                $start_date = $employee->attendance_in_date;
            }

            // create the range between the start and end dates
            $start = new DateTime($start_date);
            $end = new DateTime($end_date . ' +1 day');
            $interval = DateInterval::createFromDateString('1 day');
            $date_range = new DatePeriod($start, $interval, $end);

            // loop through all the days between start and end date
            foreach ($date_range as $date) {
                $the_day = $date->format('Y-m-d');
                $day_of_week = date('D', strtotime($the_day));

                // check if this date is friday
                if ($day_of_week == 'Fri') {
                    continue;
                } else {
                    // check if this date is holiday
                    $is_holiday = in_array($the_day, $holidays);
                    if ($is_holiday == true) {
                        continue;
                    } else {
                        // check if in this date employee is in leave
                        $is_in_leave = $this->isEmployeeInLeave($the_day, $leaves);
                        if ($is_in_leave == true) {
                            continue;
                        } else {
                            // check if employee is present at this date
                            $is_present = $this->isEmployeePresent($the_day, $images);
                            if ($is_present == true) {
                                $shamsi_date_arr = dateTo($the_day, 'shamsi', true);
                                $shamsi_date = $shamsi_date_arr[0] . '-' . $shamsi_date_arr[1] . '-' . $shamsi_date_arr[2];
                                array_push($present_days, $shamsi_date);
                            }
                        }
                    }
                }
            }

            return $present_days;
        } catch (\Exception $ex) {
            return [];
        }
    }

    private function getEmployeeAbsentDays($employee, $start_date, $end_date, $images, $holidays, $leaves)
    {
        try {
            $absent_days = array();

            if (!is_null($employee->attendance_in_date) && $start_date < $employee->attendance_in_date) {
                $start_date = $employee->attendance_in_date;
            }

            // create the range between the start and end dates
            $start = new DateTime($start_date);
            $end = new DateTime($end_date . ' +1 day');
            $interval = DateInterval::createFromDateString('1 day');
            $date_range = new DatePeriod($start, $interval, $end);

            // loop through all the days between start and end date
            foreach ($date_range as $date) {
                $the_day = $date->format('Y-m-d');
                $day_of_week = date('D', strtotime($the_day));

                // check if this date is friday
                if ($day_of_week == 'Fri') {
                    continue;
                } else {
                    // check if this date is holiday
                    $is_holiday = in_array($the_day, $holidays);
                    if ($is_holiday == true) {
                        continue;
                    } else {
                        // check if in this date employee is in leave
                        $is_in_leave = $this->isEmployeeInLeave($the_day, $leaves);
                        if ($is_in_leave == true) {
                            continue;
                        } else {
                            // check if employee is present at this date
                            $is_present = $this->isEmployeePresent($the_day, $images);
                            if ($is_present == true) {
                                continue;
                            } else {
                                $shamsi_date_arr = dateTo($the_day, 'shamsi', true);
                                $shamsi_date = $shamsi_date_arr[0] . '-' . $shamsi_date_arr[1] . '-' . $shamsi_date_arr[2];
                                array_push($absent_days, $shamsi_date);
                            }
                        }
                    }
                }
            }

            return $absent_days;
        } catch (\Exception $ex) {
            return [];
        }
    }

    private function getEmployeeAttendanceDays($employee, $start_date, $end_date, $images, $holidays, $leaves)
    {

        try {
            $present_days = [];
            $absent_days = [];

            if (!is_null($employee->attendance_in_date) && $start_date < $employee->attendance_in_date) {
                $start_date = $employee->attendance_in_date;
            }

            $start = new DateTime($start_date);
            $end = new DateTime($end_date . ' +1 day');
            $interval = DateInterval::createFromDateString('1 day');
            $date_range = new DatePeriod($start, $interval, $end);

            foreach ($date_range as $date) {
                $the_day = $date->format('Y-m-d');
                $day_of_week = $date->format('D');

                if (
                    $day_of_week === 'Fri' ||
                    in_array($the_day, $holidays) ||
                    $this->isEmployeeInLeave($the_day, $leaves)
                ) {
                    continue;
                }

                $shamsi_date_arr = dateTo($the_day, 'shamsi', true);
                $shamsi_date = implode('-', $shamsi_date_arr);

                if ($this->isEmployeePresent($the_day, $images)) {
                    $present_days[] = $shamsi_date;
                } else {
                    $absent_days[] = $shamsi_date;
                }
            }

            return [
                'present' => $present_days,
                'absent' => $absent_days,
            ];
        } catch (\Exception $ex) {
            return [
                'present' => [],
                'absent' => [],
            ];
        }
    }


    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | 3. START DAILY ATTENDANCE REPORT راپور حاضری روزانه
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */
    /**
     * Get daily attendance report page
     */
    public function getEmployeesDailyAttendanceReport(Request $request)
    {

        try {
            if (canDo('attendance-daily_att_report')) {
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_statuses'] = $this->employeeRepository->getEmployeeStatuses();

                return view('pages.attendance.reports.daily_attendance', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'attendance.reports.daily-attendance.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employees for dailt attendance report
     */
    public function searchEmployeesForDailyAttendanceReport(Request $request)
    {

        try {
            if (canDo('attendance-daily_att_report')) {
                $data = $this->attendanceRepository->searchEmployeesReport($request, 'paginate');

                return view('components.pages.attendance.reports.daily_attendance_datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.search_failed'), null, $ex);
            //throw $ex;
        }
    }

    /**
     * Get employees daily attendance report
     */
    public function getEmployeesDailyAttendanceExcelReport(DailyAttendanceReportRequest $request)
    {
        try {
            if (canDo('attendance-daily_att_report')) {
                // employees
                $employees = $this->attendanceRepository->searchEmployeesReport($request, 'all');
                $rfids = $employees->pluck('id');

                $images = [];
                if (!is_null($rfids) && count($rfids) > 0) {
                    $images = $this->attendanceRepository->getEmployeeImagesByRFIDs($rfids, $request->date);
                }

                // file name
                if ($request->is_special_report == 1) {
                    $machines = $this->machinesRepository->lists();
                    $file_name = trans('attendance/att.employees_special_daily_attendance_export_to_excel_file_title', ['date' => dateTo($request->date, 'shamsi', false)]);
                    $excel = new EmployeesSpecialDailyAttendanceExcelReport($employees, $images, $machines, $file_name);
                } else {
                    $file_name = trans('attendance/att.employees_daily_attendance_export_to_excel_file_title', ['date' => dateTo($request->date, 'shamsi', false)]);
                    $excel = new EmployeesDailyAttendanceExcelReport($employees, $images, $file_name);
                }

                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'attendance/exports/reports/daily_attendance/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }
    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | END DAILY ATTENDANCE REPORT راپور حاضری روزانه
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */




    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | 4. START TIME BASED ATTENDANCE REPORT راپور حاضری به اساس وقت
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */
    /**
     * Get time based attendance report page
     */
    public function getEmployeesTimeBasedAttendanceReport(Request $request)
    {

        try {
            if (canDo('attendance-report_based_on_time')) {
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_statuses'] = $this->employeeRepository->getEmployeeStatuses();

                return view('pages.attendance.reports.time_based_attendance', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'attendance.reports.time-based-attendance.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employees for time based attendance report
     */
    public function searchEmployeesForTimeBasedAttendanceReport(Request $request)
    {
        try {
            if (canDo('attendance-report_based_on_time')) {
                $data = $this->attendanceRepository->searchEmployeesReport($request, 'paginate');

                return view('components.pages.attendance.reports.time_based_attendance_datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.search_failed'), null, $ex);

            //throw $ex;
        }
    }

    /**
     * Get employees time based attendance report
     */
    public function getEmployeesTimeBasedAttendanceExcelReport(TimeBasedAttendanceReportRequest $request)
    {


        try {
            if (canDo('documents-emp_view_promotions_info')) {
                $date_type = $request->date_type;
                $month = $request->month;
                $year = $request->year;
                $start_date = null;
                $end_date = null;

                // set start_date and end_date
                // monthly
                if ($date_type == 1) {
                    if (!is_null($month) && $month != '' && $month != 0) {
                        // check if year is selected, if not then get current year
                        $the_year = !is_null($year) && $year != '' && $year != 0 ? $year : getCurrentShamsiYear();

                        // month first date
                        $start_date = dateTo(('01-' . $month . '-' . $the_year), 'miladi', false);

                        // month last date
                        if ($month >= 1 && $month <= 6) { // [حمل، ثور، جوزا، سرطان، اسد، سنبله]
                            $end_date = dateTo(('31-' . $month . '-' . $the_year), 'miladi', false);
                        } else if ($month == 12) { // [حوت]
                            $end_date = dateTo((isLeapYear($the_year) ? '30-' . $month . '-' . $the_year : '29-' . $month . '-' . $the_year), 'miladi', false);
                        } else { // [میزان، عقرب، قوس، جدی، دلو]
                            $end_date = dateTo(('30-' . $month . '-' . $the_year), 'miladi', false);
                        }
                    }
                } else {
                    $start_date = $request->start_date;
                    $end_date = $request->end_date;
                }

                // employees
                $employees = $this->attendanceRepository->searchEmployeesReport($request, 'all');
                $rfids = $employees->pluck('id');

                $images = [];
                if (!is_null($rfids) && count($rfids) > 0) {
                    $images = $this->attendanceRepository->getEmployeeImagesBetweenDatesByRFIDs($rfids, $start_date, $end_date);
                }

                // file name
                if ($date_type == 1) {
                    $file_name = trans('attendance/att.employees_monthly_time_based_attendance_export_to_excel_file_title', ['year' => $year, 'month' => config('custom.shamsi.months.' . app()->getLocale() . '.' . $month), 'date' => getCurrentShamsiDate()]);
                } else {
                    $file_name = trans('attendance/att.employees_choise_date_time_based_attendance_export_to_excel_file_title', ['start_date' => dateTo($start_date, 'shamsi', false), 'end_date' => dateTo($end_date, 'shamsi', false), 'date' => getCurrentShamsiDate()]);
                }

                $excel = new EmployeesTimeBasedAttendanceExcelReport($employees, $images, $start_date, $end_date, $file_name);

                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'attendance/exports/reports/time_based_attendance/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));
            //throw $ex;
        }
    }
    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | END TIME BASED ATTENDANCE REPORT راپور حاضری به اساس وقت
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */





    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | 5. START E-ATTENDANCE EMPLOYEES REPORT راپور کارمندان شامل حاضری الکترونیک
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */
    /**
     * Get e-attendance employees report page
     */
    public function getEAttendanceEmployeesReport(Request $request)
    {
        try {
            if (canDo('attendance-electronic_attendance_report')) {
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_statuses'] = $this->employeeRepository->getEmployeeStatuses();

                return view('pages.attendance.reports.e_attendance_employees', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'attendance.reports.e-attendance-employees.search', 'jsCallBack' => 'setTotals()'])]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }

    /**
     * Search employees for e-attendance report
     */
    public function searchEAttendanceEmployeesReport(Request $request)
    {

        try {
            if (canDo('attendance-electronic_attendance_report')) {
                $data = $this->attendanceRepository->searchEAttendanceEmployeesReport($request, 'paginate');

                return view('components.pages.attendance.reports.e_attendance_employees_datatable', ['data' => $data]);
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.search_failed'), null, $ex);

            //throw $ex;
        }
    }

    /**
     * Get e-attendance employees report
     */
    public function getEAttendanceEmployeesExcelReport(EAttendanceEmployeesReportRequest $request)
    {

        try {
            if (canDo('attendance-electronic_attendance_report')) {
                // employees
                $employees = $this->attendanceRepository->searchEAttendanceEmployeesReport($request, 'all');

                // file name
                $file_name = trans('attendance/att.e_attendance_employees_export_to_excel_file_title', ['date' => getCurrentShamsiDate()]);
                $excel = new EAttendanceEmployeesExcelReport($employees, $file_name);

                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'attendance/exports/reports/e_attendance_employees/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            // return notAllowed();
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return redirect()->back()->with('error', __('general.something_wrong_happened'));

            //throw $ex;
        }
    }
    /*
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    | END E-ATTENDANCE EMPLOYEES REPORT راپور کارمندان شامل حاضری الکترونیک
    |---------------------------------------------------------------------------------------------------------------------------------------------------------|
    */




    /**
     * Check if employee has a leave record in the @param date
     */
    private function isEmployeeInLeave($date, $leaves)
    {
        try {
            foreach ($leaves as $leave) {
                if ($date >= $leave->date_from && $date <= $leave->date_to) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $ex) {
            return null;
        }
    }

    /**
     * Check if employee has two pictures in the @param date
     */
    private function isEmployeePresent($date, $images)
    {
        try {
            $current_date_images = [];
            foreach ($images as $image) {
                if ($image->date == $date) {
                    array_push($current_date_images, $image);
                }
            }
            if (count($current_date_images) >= 2) {
                return true;
            }
            return false;
        } catch (\Exception $ex) {
            return false;
        }
    }
}
