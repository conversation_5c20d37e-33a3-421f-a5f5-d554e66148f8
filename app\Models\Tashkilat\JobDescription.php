<?php

namespace App\Models\Tashkilat;

use App\Traits\ActivityLogCustom;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;

class JobDescription extends Model
{
    use HasFactory;
    use HasFactory,ActivityLogCustom,LogsActivity;
       protected $table = 'job_descriptions';

    public $fillable = [
        'tashkilat_id',
        'content',
        'created_by',
      
    ];
}
