<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_images', function (Blueprint $table) {
            $table->id();
            $table->string('RFID', 20);
            $table->string('path', 300);
            $table->integer('time');
            $table->integer('year');
            $table->integer('month');
            $table->integer('day');
            $table->date('date');
            $table->integer('status')->default(0)->comment('0:accepted:default, 0:rejected');
            $table->string('machine', 30);
            $table->unsignedBigInteger('att_station_id')->nullable();
            $table->tinyInteger('is_rejected')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('rejected_by')->nullable();
            $table->dateTime('rejected_at')->nullable();
            $table->timestamps();
            // Indexes
            $table->index('RFID');
            $table->index('date');
            $table->index('att_station_id');
            //composit 
            $table->index(['date', 'machine', 'time'], 'attendance_date_machine_time_index'); 

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_images');
    }
};
